import type { Node } from '@swc/core';
import type { AsyncRuleEngine } from '../engine/types';
import { PositionConverter } from '../utils/position-converter';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { RuleRegistry } from './rule-registry';
import type {
  CalculationOptions,
  EmergencyPositionContext,
  ErrorRecoveryStep,
  FunctionResult,
  TokenSearchRange,
} from './types';
import { DiagnosticMarker, RuleCategory } from './types';
import { getPositionStrategy } from './visitor/utils';

// 初始化默认规则到RuleRegistry
RuleRegistry.register('if-statement', 'if 语句', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('while-statement', 'while 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('do-while-statement', 'do-while 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('for-statement', 'for 循环', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('switch-statement', 'switch 语句', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('try-statement', 'try 语句', RuleCategory.EXCEPTION_HANDLING, 0);
RuleRegistry.register('catch-clause', 'catch 异常处理', RuleCategory.EXCEPTION_HANDLING, 1);
RuleRegistry.register('conditional-expression', '三元运算符', RuleCategory.CONTROL_FLOW, 1);
RuleRegistry.register('logical-operator', '逻辑运算符', RuleCategory.LOGICAL_OPERATOR, 1);
RuleRegistry.register('recursive-call', '递归调用', RuleCategory.RECURSION, 1);
RuleRegistry.register('logical-operator-mixing', '逻辑运算符混用惩罚', RuleCategory.LOGICAL_OPERATOR, 1);

/**
 * SWC 节点的 span 类型定义
 */
interface SwcSpan {
  start: number;
  end: number;
  ctxt: number;
}

/**
 * 具有 span 属性的节点类型
 */
interface NodeWithSpan extends Node {
  span: SwcSpan;
}

/**
 * ComplexityVisitor - 认知复杂度计算访问者
 *
 * 基于访问者模式实现的复杂度计算器，专门为单个函数计算认知复杂度。
 * 实现结构化的 span 修正策略，优先使用父节点信息进行位置回退。
 * 支持异步规则引擎和传统规则注册表的双模式运行。
 *
 * L1 层策略映射表增强：
 * - 实现基于节点类型的智能位置定位策略
 * - 支持多级回退机制（节点策略 → 父节点 → 默认位置）
 * - 提供 Token 查找系统支持精确关键字定位
 * - 运行时可扩展的策略映射表
 *
 * 核心功能：
 * - 遍历单个函数的 AST 节点并计算认知复杂度
 * - 维护嵌套层级，支持嵌套结构的复杂度计算
 * - 实现基于父节点的结构化 span 修正
 * - 与 DetailCollector 集成，收集详细的计算步骤
 * - 存储多个函数的分析结果
 * - 支持 AsyncRuleEngine 和 RuleRegistry 的双模式
 *
 * 设计原则：
 * - 遵循访问者模式，分离 AST 结构遍历和复杂度计算逻辑
 * - 实现"结构性修正层"，基于 AST 父节点信息进行精确的 span 修正
 * - 支持详细的复杂度计算日志记录和调试
 * - 错误恢复机制，确保部分节点失败不影响整体计算
 */
export class ComplexityVisitor extends BaseVisitor {
  // =============================================================================
  // L1 层策略映射表 - 节点位置定位策略系统
  // =============================================================================

  /**
   * 源代码内容，用于位置转换和 span 修正
   */
  private readonly sourceCode: string;

  /**
   * 详细信息收集器，用于记录复杂度计算步骤
   */
  private readonly detailCollector?: DetailCollector;

  /**
   * 计算选项，用于配置复杂度计算行为
   */
  private readonly options: CalculationOptions;

  /**
   * 异步规则引擎实例（优先使用）
   */
  private readonly asyncRuleEngine?: AsyncRuleEngine;

  /**
   * 函数分析结果存储
   */
  private results: FunctionResult[] = [];

  /**
   * 当前累计复杂度
   */
  private totalComplexity: number = 0;

  /**
   * 当前嵌套层级
   * 用于计算嵌套结构（如 if、for、while 等）的复杂度增量
   */
  private nestingLevel: number = 0;

  /**
   * 当前正在分析的函数名称
   */
  private currentFunctionName: string = '';

  /**
   * 当前函数的起始位置
   */
  private currentFunctionLocation: { line: number; column: number } = { line: 0, column: 0 };

  /**
   * 已处理的混用表达式节点集合
   * 用于确保同一表达式树不重复应用混用惩罚
   */
  private processedMixingNodes: Set<any> = new Set();

  /**
   * 构造函数
   * @param sourceCode 源代码内容，用于位置转换
   * @param detailCollector 可选的详细信息收集器
   * @param options 可选的计算选项
   * @param asyncRuleEngine 可选的异步规则引擎（如果提供，将优先使用）
   */
  constructor(
    sourceCode: string = '',
    detailCollector?: DetailCollector,
    options: CalculationOptions = {},
    asyncRuleEngine?: AsyncRuleEngine
  ) {
    super();
    this.sourceCode = sourceCode;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;
  }

  /**
   * 获取所有函数的分析结果
   * @returns 函数分析结果数组
   */
  public getResults(): FunctionResult[] {
    return [...this.results]; // 返回副本确保不可变
  }

  /**
   * 分析单个函数的入口方法
   * 初始化函数分析上下文，遍历函数体，收集结果
   * @param node 函数节点（FunctionDeclaration、ArrowFunctionExpression等）
   */
  public visitFunction(node: any): void {
    try {
      // 重置状态
      this.resetForNewFunction();

      // 提取函数信息
      this.currentFunctionName = this.extractFunctionName(node);
      this.currentFunctionLocation = this.getNodeLocation(node);

      // 开始详细模式跟踪（如果启用）
      if (this.detailCollector) {
        this.detailCollector.startFunction(
          this.currentFunctionName,
          this.currentFunctionLocation.line,
          this.currentFunctionLocation.column
        );
      }

      // 遍历函数体
      const functionBody = this.getFunctionBody(node);
      if (functionBody) {
        this.visit(functionBody);
      }

      // 结束详细模式跟踪
      let functionDetail = null;
      if (this.detailCollector) {
        functionDetail = this.detailCollector.endFunction();
      }

      // 收集结果
      const result: FunctionResult = {
        name: this.currentFunctionName,
        complexity: this.totalComplexity,
        line: this.currentFunctionLocation.line,
        column: this.currentFunctionLocation.column,
        filePath: '', // 将由调用者设置
        details: functionDetail?.details,
      };

      this.results.push(result);
    } catch (error) {
      // 函数级错误处理
      this.handleFunctionAnalysisError(node, error as Error);
    }
  }

  /**
   * 重置状态以分析新函数
   */
  private resetForNewFunction(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.processedMixingNodes.clear();
    this.currentFunctionName = '';
    this.currentFunctionLocation = { line: 0, column: 0 };
    this.reset(); // 调用父类的重置方法清空父节点栈
  }

  /**
   * 提取函数名称
   * @param node 函数节点
   * @returns 函数名称
   */
  private extractFunctionName(node: any): string {
    // 函数声明
    if (node.type === 'FunctionDeclaration' && node.identifier) {
      return node.identifier.value || node.identifier.name || '<anonymous>';
    }

    // 方法定义
    if (node.type === 'MethodDefinition' && node.key) {
      return node.key.value || node.key.name || '<method>';
    }

    // 类方法
    if (node.type === 'ClassMethod' && node.key) {
      return node.key.value || node.key.name || '<method>';
    }

    // 变量声明中的函数表达式
    if (node.type === 'VariableDeclarator' && node.id) {
      return node.id.value || node.id.name || '<anonymous>';
    }

    // 属性中的函数
    if (node.type === 'Property' && node.key) {
      return node.key.value || node.key.name || '<property>';
    }

    // 箭头函数等其他情况
    return '<anonymous>';
  }

  /**
   * 获取函数体节点
   * @param node 函数节点
   * @returns 函数体节点
   */
  private getFunctionBody(node: any): any {
    // 函数声明
    if (node.type === 'FunctionDeclaration' && node.body) {
      return node.body;
    }

    // 箭头函数
    if (node.type === 'ArrowFunctionExpression') {
      return node.body;
    }

    // 函数表达式
    if (node.type === 'FunctionExpression' && node.body) {
      return node.body;
    }

    // 方法定义
    if (node.type === 'MethodDefinition' && node.value && node.value.body) {
      return node.value.body;
    }

    // 类方法
    if (node.type === 'ClassMethod' && node.function && node.function.body) {
      return node.function.body;
    }

    // 变量声明中的函数
    if (node.type === 'VariableDeclarator' && node.init) {
      return this.getFunctionBody(node.init);
    }

    // 属性中的函数
    if (node.type === 'Property' && node.value) {
      return this.getFunctionBody(node.value);
    }

    return null;
  }

  /**
   * 获取节点位置信息
   * @param node 节点
   * @returns 位置信息
   */
  private getNodeLocation(node: any): { line: number; column: number } {
    try {
      // 尝试方法1：使用有效的span信息
      if (this.isValidSpan(node)) {
        const position = PositionConverter.spanToPosition(this.sourceCode, (node as NodeWithSpan).span.start);
        // 验证位置信息的合理性
        if (position.line >= 1 && position.column >= 0) {
          return { line: position.line, column: position.column };
        }
      }

      // 尝试方法2：检查节点是否有直接的位置属性
      if (node.loc) {
        const loc = node.loc;
        if (loc.start && typeof loc.start.line === 'number' && typeof loc.start.column === 'number') {
          if (loc.start.line >= 1 && loc.start.column >= 0) {
            return { line: loc.start.line, column: loc.start.column };
          }
        }
      }

      // 尝试方法3：检查是否有行列属性
      if (typeof node.line === 'number' && typeof node.column === 'number') {
        if (node.line >= 1 && node.column >= 0) {
          return { line: node.line, column: node.column };
        }
      }

      // 尝试方法4：如果是函数节点，尝试从标识符获取位置
      if (
        node.type === 'FunctionDeclaration' ||
        node.type === 'FunctionExpression' ||
        node.type === 'ArrowFunctionExpression' ||
        node.type === 'MethodDefinition'
      ) {
        const identifier = node.key || node.id;
        if (identifier && this.isValidSpan(identifier)) {
          const position = PositionConverter.spanToPosition(this.sourceCode, (identifier as NodeWithSpan).span.start);
          if (position.line >= 1 && position.column >= 0) {
            return { line: position.line, column: position.column };
          }
        }
      }
    } catch (error) {
      // 位置转换失败时的调试信息
      if (this.options?.enableDebugLog) {
        console.debug('Position extraction failed:', {
          nodeType: node.type,
          hasSpan: !!node.span,
          hasLoc: !!node.loc,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // 所有方法都失败，返回默认值，但在调试模式下记录问题
    if (this.options?.enableDebugLog) {
      console.warn(`Unable to extract valid position for node type: ${node.type}, falling back to (1,0)`);
    }

    return { line: 1, column: 0 };
  }

  /**
   * 处理函数分析错误
   * @param node 函数节点
   * @param error 错误信息
   */
  private handleFunctionAnalysisError(node: any, error: Error): void {
    const functionName = this.extractFunctionName(node);
    const location = this.getNodeLocation(node);

    console.warn(`Error analyzing function ${functionName}:`, error.message);

    // 添加错误结果
    const errorResult: FunctionResult = {
      name: functionName,
      complexity: 0,
      line: location.line,
      column: location.column,
      filePath: '',
      severity: 'Critical',
    };

    this.results.push(errorResult);

    // 记录到详细日志
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: location.line,
            column: location.column,
            increment: 0,
            ruleId: 'function-analysis-error',
            description: `函数分析失败: ${functionName}`,
            context: error.message,
          },
          `函数 ${functionName} 分析过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record function analysis error for ${functionName}:`, detailError);
      }
    }
  }

  /**
   * 获取当前累计的总复杂度（向后兼容方法）
   * @returns 总复杂度值
   */
  public getTotalComplexity(): number {
    return this.totalComplexity;
  }

  /**
   * 重写基类的visit方法以正确处理嵌套层级
   * 同时自动检测函数节点并进行函数级分析
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  public override visit<T extends Node>(node: T): T {
    // 如果是顶层调用且为模块或程序节点，查找所有函数并分析
    if (this.parentStack.length === 0 && (node.type === 'Module' || node.type === 'Program')) {
      return this.visitModuleAndAnalyzeFunctions(node);
    }

    // 推入当前节点作为子节点的父节点
    this.parentStack.push(node);

    try {
      // 检查是否是嵌套结构节点，如果是先计算复杂度
      const shouldEnterNesting = this.shouldEnterNesting(node);

      // 计算当前节点的复杂度
      this.calculateNodeComplexity(node);

      // 如果需要进入嵌套，在访问子节点前增加嵌套层级
      if (shouldEnterNesting) {
        this.enterNesting();
      }

      try {
        // 访问子节点
        this.visitChildren(node);
      } finally {
        // 如果进入了嵌套，在访问完子节点后退出嵌套
        if (shouldEnterNesting) {
          this.exitNesting();
        }
      }

      return node;
    } catch (error) {
      // 错误恢复：记录错误但继续处理其他节点
      this.handleVisitError(node, error as Error);
      return node;
    } finally {
      // 确保在任何情况下都会弹出栈
      this.parentStack.pop();
    }
  }

  /**
   * 访问模块节点并自动分析所有函数
   * @param node 模块或程序节点
   * @returns 处理后的节点
   */
  private visitModuleAndAnalyzeFunctions<T extends Node>(node: T): T {
    // 找到所有函数节点
    const functions = this.findFunctionNodes(node);

    // 对每个函数节点调用 visitFunction
    for (const functionNode of functions) {
      this.visitFunction(functionNode);
    }

    return node;
  }

  /**
   * 在 AST 中查找所有函数节点
   * @param node 根节点
   * @returns 函数节点数组
   */
  private findFunctionNodes(node: any): any[] {
    const functions: any[] = [];

    const traverse = (currentNode: any) => {
      if (!currentNode) return;

      // 检查是否是函数节点
      if (this.isFunctionNode(currentNode)) {
        functions.push(currentNode);
        return; // 不深入函数内部查找嵌套函数（避免重复计算）
      }

      // 递归遍历子节点
      if (currentNode.body && Array.isArray(currentNode.body)) {
        for (const child of currentNode.body) {
          traverse(child);
        }
      } else if (currentNode.body && typeof currentNode.body === 'object') {
        traverse(currentNode.body);
      }

      // 处理其他可能包含函数的节点类型
      if (currentNode.declarations && Array.isArray(currentNode.declarations)) {
        for (const decl of currentNode.declarations) {
          traverse(decl);
        }
      }

      if (currentNode.init) {
        traverse(currentNode.init);
      }

      if (currentNode.consequent) {
        traverse(currentNode.consequent);
      }

      if (currentNode.alternate) {
        traverse(currentNode.alternate);
      }
    };

    traverse(node);
    return functions;
  }

  /**
   * 判断节点是否是函数节点
   * @param node 节点
   * @returns 是否是函数节点
   */
  private isFunctionNode(node: any): boolean {
    return (
      node &&
      (node.type === 'FunctionDeclaration' ||
        node.type === 'FunctionExpression' ||
        node.type === 'ArrowFunctionExpression' ||
        node.type === 'MethodDefinition' ||
        node.type === 'ClassMethod')
    );
  }

  /**
   * 判断节点是否应该影响嵌套层级
   * @param node 节点
   * @returns 是否应该影响嵌套层级
   */
  private shouldEnterNesting(node: Node): boolean {
    switch (node.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'DoWhileStatement':
      case 'ForStatement':
      case 'ForInStatement':
      case 'ForOfStatement':
      case 'SwitchStatement':
      case 'CatchClause':
        return true;
      default:
        return false;
    }
  }

  /**
   * 访问节点的核心实现（已弃用，由visit方法直接处理）
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  protected visitNode(node: Node): Node {
    // 这个方法不再使用，逻辑已迁移到visit方法中
    return node;
  }

  /**
   * 根据节点类型计算复杂度
   * 使用规则引擎委托进行复杂度评估
   * @param node AST 节点
   */
  private calculateNodeComplexity(node: Node): void {
    // 委托给规则引擎获取复杂度
    const { complexity, ruleId, description } = this.evaluateNodeWithRules(node);

    if (complexity > 0) {
      // 添加复杂度
      this.addComplexity(complexity, node, ruleId, description, this.getContextForRule(ruleId));
    }
  }

  /**
   * 使用规则引擎评估节点复杂度
   * 优先使用 AsyncRuleEngine，如果不可用或失败则回退到 RuleRegistry
   * @param node AST 节点
   * @returns 评估结果
   */
  private evaluateNodeWithRules(node: Node): { complexity: number; ruleId: string; description: string } {
    // 如果有异步规则引擎，尝试使用它（但由于这是同步调用，我们需要特殊处理）
    if (this.asyncRuleEngine) {
      try {
        // 异步引擎可以处理，但我们在同步环境中，所以使用 RuleRegistry 作为回退
        console.debug('AsyncRuleEngine available but in sync context, using RuleRegistry fallback');
      } catch (asyncError) {
        console.debug('AsyncRuleEngine check failed, using RuleRegistry fallback');
      }
    }

    // 使用传统 RuleRegistry 进行评估
    const ruleId = this.getRuleIdForNodeType(node.type, node);
    const rule = RuleRegistry.getRule(ruleId);

    if (!rule || !rule.enabled) {
      return { complexity: 0, ruleId: 'unknown', description: 'Unknown rule' };
    }

    // 检查节点是否需要特殊处理
    if (this.shouldSkipNode(node)) {
      return { complexity: 0, ruleId, description: rule.description };
    }

    // 计算基础复杂度 + 嵌套增量
    const baseComplexity = rule.defaultIncrement;
    const nestingIncrement = this.getNestingIncrement();
    const totalComplexity = baseComplexity + nestingIncrement;

    // 对于逻辑运算符，还需要检查混用惩罚
    if (ruleId === 'logical-operator') {
      this.checkAndApplyMixingPenaltyOnce(node as any);
    }

    return {
      complexity: totalComplexity,
      ruleId,
      description: rule.description,
    };
  }

  /**
   * 根据节点类型获取规则ID
   * @param nodeType 节点类型
   * @param node 可选的节点实例，用于更精确的判断
   * @returns 规则ID
   */
  private getRuleIdForNodeType(nodeType: string, node?: Node): string {
    const ruleMap: Record<string, string> = {
      IfStatement: 'if-statement',
      WhileStatement: 'while-statement',
      DoWhileStatement: 'do-while-statement',
      ForStatement: 'for-statement',
      ForInStatement: 'for-statement',
      ForOfStatement: 'for-statement',
      SwitchStatement: 'switch-statement',
      TryStatement: 'try-statement',
      CatchClause: 'catch-clause',
      ConditionalExpression: 'conditional-expression',
      LogicalExpression: 'logical-operator',
      CallExpression: 'recursive-call',
    };

    // 特殊处理 BinaryExpression：只有真正的逻辑运算符才映射到 logical-operator
    if (nodeType === 'BinaryExpression') {
      if (node && this.isLogicalOperator(node as any)) {
        return 'logical-operator';
      }
      // 非逻辑运算符的 BinaryExpression 不应该被处理
      return 'unknown-rule';
    }

    return ruleMap[nodeType] || 'unknown-rule';
  }

  /**
   * 检查是否应跳过节点的复杂度计算
   * @param node AST 节点
   * @returns 是否跳过
   */
  private shouldSkipNode(node: Node): boolean {
    // 逻辑运算符特殊处理
    if (node.type === 'BinaryExpression' || node.type === 'LogicalExpression') {
      const logicalNode = node as any;
      if (!this.isLogicalOperator(logicalNode)) {
        return true; // 不是逻辑运算符，跳过
      }
      if (this.isDefaultValueAssignment(logicalNode)) {
        return true; // 是默认值赋值，跳过
      }
    }

    // 递归调用特殊处理
    if (node.type === 'CallExpression') {
      return !this.isRecursiveCall(node as any);
    }

    return false;
  }

  /**
   * 获取规则的上下文信息
   * @param ruleId 规则ID
   * @returns 上下文信息
   */
  private getContextForRule(ruleId: string): string {
    const baseContext = `嵌套层级: ${this.nestingLevel}`;

    switch (ruleId) {
      case 'logical-operator':
        return baseContext;
      case 'recursive-call':
        return '递归函数调用';
      default:
        return `基础分(1) + 嵌套分(${this.getNestingIncrement()})`;
    }
  }

  /**
   * Span 验证和修正系统 - Task 11 增强版
   *
   * 采用完善的多级错误恢复机制，包含详细的错误记录和诊断信息：
   * 1. 尝试主要位置策略（节点策略 → 原始span → 父节点策略）
   * 2. 智能父节点选择和回退机制
   * 3. 紧急位置生成（基于上下文的智能默认位置）
   * 4. 完整的错误恢复步骤记录和诊断信息输出
   *
   * @param node 要验证的 AST 节点
   * @returns 有效的字节偏移位置
   */
  private validateSpan(node: Node): number {
    const recoverySteps: ErrorRecoveryStep[] = [];
    const startTime = Date.now();
    let attemptCount = 0;

    try {
      // 步骤 1: 尝试主要策略（节点策略 → 原始span）
      const primaryResult = this.attemptPrimaryStrategy(node, recoverySteps, ++attemptCount);
      if (primaryResult !== null) {
        this.recordRecoverySuccess(node, primaryResult, 'primary', recoverySteps, Date.now() - startTime);
        return primaryResult;
      }

      // 步骤 2: 尝试原始span验证
      const originalResult = this.attemptOriginalSpan(node, recoverySteps, ++attemptCount);
      if (originalResult !== null) {
        this.recordRecoverySuccess(node, originalResult, 'original', recoverySteps, Date.now() - startTime);
        return originalResult;
      }

      // 步骤 3: 智能父节点回退机制
      const parentResult = this.attemptParentSpanFallbackWithRecovery(node, recoverySteps, ++attemptCount);
      if (parentResult !== null) {
        this.recordRecoverySuccess(node, parentResult, 'parent', recoverySteps, Date.now() - startTime);
        return parentResult;
      }

      // 步骤 4: 紧急位置生成
      const emergencyResult = this.attemptEmergencyPositionGeneration(node, recoverySteps, ++attemptCount);
      this.recordRecoverySuccess(node, emergencyResult, 'emergency', recoverySteps, Date.now() - startTime);
      return emergencyResult;
    } catch (error) {
      // 记录完全失败的情况
      const criticalFailureStep: ErrorRecoveryStep = {
        step: ++attemptCount,
        strategyType: 'emergency',
        strategyName: 'critical-failure-fallback',
        success: false,
        position: null,
        error: `Critical error in span validation: ${error instanceof Error ? error.message : String(error)}`,
        executionTime: Date.now() - startTime,
      };
      recoverySteps.push(criticalFailureStep);

      const fallbackSpan = 0;
      this.recordRecoveryFailure(node, fallbackSpan, recoverySteps, Date.now() - startTime, error as Error);
      return fallbackSpan;
    }
  }

  /**
   * 尝试主要策略 - Task 11 增强方法
   * 先尝试策略映射，失败时记录详细信息
   */
  private attemptPrimaryStrategy(node: Node, recoverySteps: ErrorRecoveryStep[], stepNumber: number): number | null {
    const stepStartTime = Date.now();

    try {
      // 尝试使用 L1 层策略映射系统
      const strategyResult = this.applyPositionStrategy(node);

      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'primary',
        strategyName: 'l1-strategy-mapping',
        success: strategyResult !== null,
        position: strategyResult,
        executionTime: Date.now() - stepStartTime,
      };

      if (strategyResult === null) {
        step.error = `No strategy found for node type: ${node.type}`;
      }

      recoverySteps.push(step);
      return strategyResult;
    } catch (error) {
      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'primary',
        strategyName: 'l1-strategy-mapping',
        success: false,
        position: null,
        error: `Strategy execution failed: ${error instanceof Error ? error.message : String(error)}`,
        executionTime: Date.now() - stepStartTime,
      };
      recoverySteps.push(step);
      return null;
    }
  }

  /**
   * 尝试原始span验证 - Task 11 增强方法
   */
  private attemptOriginalSpan(node: Node, recoverySteps: ErrorRecoveryStep[], stepNumber: number): number | null {
    const stepStartTime = Date.now();

    try {
      if (this.isValidSpan(node)) {
        const originalSpan = (node as NodeWithSpan).span.start;

        const step: ErrorRecoveryStep = {
          step: stepNumber,
          strategyType: 'primary',
          strategyName: 'original-span-validation',
          success: true,
          position: originalSpan,
          executionTime: Date.now() - stepStartTime,
        };
        recoverySteps.push(step);
        return originalSpan;
      } else {
        const step: ErrorRecoveryStep = {
          step: stepNumber,
          strategyType: 'primary',
          strategyName: 'original-span-validation',
          success: false,
          position: null,
          error: 'Node span is invalid or missing',
          executionTime: Date.now() - stepStartTime,
        };
        recoverySteps.push(step);
        return null;
      }
    } catch (error) {
      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'primary',
        strategyName: 'original-span-validation',
        success: false,
        position: null,
        error: `Span validation failed: ${error instanceof Error ? error.message : String(error)}`,
        executionTime: Date.now() - stepStartTime,
      };
      recoverySteps.push(step);
      return null;
    }
  }

  /**
   * 智能父节点回退机制 - Task 11 增强版
   * 跳过无意义的中间节点，找到真正有意义的父节点
   */
  private attemptParentSpanFallbackWithRecovery(
    node: Node,
    recoverySteps: ErrorRecoveryStep[],
    stepNumber: number
  ): number | null {
    const stepStartTime = Date.now();

    try {
      // 使用智能父节点选择，跳过无意义的中间节点
      const intelligentParent = this.findIntelligentParentNode(node);

      if (intelligentParent && intelligentParent !== node) {
        // 递归验证父节点的span
        const parentSpan = this.validateSpanForParent(intelligentParent);

        const step: ErrorRecoveryStep = {
          step: stepNumber,
          strategyType: 'parent',
          strategyName: 'intelligent-parent-fallback',
          success: parentSpan !== null,
          position: parentSpan,
          executionTime: Date.now() - stepStartTime,
        };

        if (parentSpan === null) {
          step.error = `Intelligent parent node (${intelligentParent.type}) also has invalid span`;
        }

        recoverySteps.push(step);
        return parentSpan;
      } else {
        const step: ErrorRecoveryStep = {
          step: stepNumber,
          strategyType: 'parent',
          strategyName: 'intelligent-parent-fallback',
          success: false,
          position: null,
          error: 'No intelligent parent node found',
          executionTime: Date.now() - stepStartTime,
        };
        recoverySteps.push(step);
        return null;
      }
    } catch (error) {
      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'parent',
        strategyName: 'intelligent-parent-fallback',
        success: false,
        position: null,
        error: `Parent fallback failed: ${error instanceof Error ? error.message : String(error)}`,
        executionTime: Date.now() - stepStartTime,
      };
      recoverySteps.push(step);
      return null;
    }
  }

  /**
   * 紧急位置生成 - Task 11 增强版
   * 使用智能保底位置生成器，基于上下文生成有意义的位置
   */
  private attemptEmergencyPositionGeneration(
    node: Node,
    recoverySteps: ErrorRecoveryStep[],
    stepNumber: number
  ): number {
    const stepStartTime = Date.now();

    try {
      // 构建紧急位置上下文
      const emergencyContext: EmergencyPositionContext = {
        originalNode: {
          type: node.type,
          span: (node as NodeWithSpan).span
            ? {
                start: (node as NodeWithSpan).span.start,
                end: (node as NodeWithSpan).span.end,
              }
            : undefined,
        },
        failedStrategies: recoverySteps.map((step) => step.strategyName),
        functionContext: this.getCurrentFunctionContext(),
        sourceMetadata: {
          totalLines: this.sourceCode.split('\n').length,
          totalLength: this.sourceCode.length,
          hasValidCode: this.sourceCode.trim().length > 0,
        },
      };

      // 使用智能保底位置生成器
      const emergencyPosition = PositionConverter.generateIntelligentDefaultPosition(this.sourceCode, emergencyContext);

      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'emergency',
        strategyName: 'intelligent-emergency-position',
        success: true,
        position: emergencyPosition,
        executionTime: Date.now() - stepStartTime,
      };
      recoverySteps.push(step);

      return emergencyPosition;
    } catch (error) {
      // 最终的兜底策略
      const step: ErrorRecoveryStep = {
        step: stepNumber,
        strategyType: 'emergency',
        strategyName: 'absolute-fallback',
        success: true,
        position: 0,
        error: `Emergency generation failed, using absolute fallback: ${
          error instanceof Error ? error.message : String(error)
        }`,
        executionTime: Date.now() - stepStartTime,
      };
      recoverySteps.push(step);

      return 0;
    }
  }

  /**
   * 记录恢复成功
   */
  private recordRecoverySuccess(
    node: Node,
    position: number,
    strategyType: 'primary' | 'original' | 'parent' | 'emergency',
    recoverySteps: ErrorRecoveryStep[],
    totalTime: number
  ): void {
    if (this.detailCollector) {
      // 使用 addStepWithDiagnostic 方法记录恢复过程
      this.detailCollector.addStepWithDiagnostic(
        {
          line: 0,
          column: 0,
          increment: 0,
          ruleId: 'error-recovery',
          description: `位置恢复成功 - 节点类型: ${node.type}`,
          context: `策略: ${strategyType}, 位置: ${position}, 步骤数: ${recoverySteps.length}, 耗时: ${totalTime}ms`,
        },
        DiagnosticMarker.NONE,
        `错误恢复成功: ${strategyType} 策略`
      );
    }
  }

  /**
   * 记录恢复失败
   */
  private recordRecoveryFailure(
    node: Node,
    fallbackPosition: number,
    recoverySteps: ErrorRecoveryStep[],
    totalTime: number,
    error: Error
  ): void {
    if (this.detailCollector) {
      // 使用 addErrorStep 方法记录严重的恢复失败
      this.detailCollector.addErrorStep(
        {
          line: 0,
          column: 0,
          increment: 0,
          ruleId: 'error-recovery-failure',
          description: `位置恢复完全失败 - 节点类型: ${node.type}`,
          context: `使用绝对回退位置: ${fallbackPosition}, 总耗时: ${totalTime}ms, 错误: ${error.message}`,
        },
        error
      );
    }
  }

  /**
   * 智能父节点查找 - 跳过无意义的中间节点
   */
  private findIntelligentParentNode(node: Node): Node | null {
    const meaninglessNodeTypes = [
      'BlockStatement',
      'Program',
      'ExpressionStatement',
      'ParenthesisExpression',
      'SequenceExpression',
    ];

    let current = this.getParentNode(node);
    let ancestorLevel = 0;
    const maxAncestorLevels = 5; // 最多向上搜索5层

    while (current && ancestorLevel < maxAncestorLevels) {
      // 跳过无意义的节点类型
      if (!meaninglessNodeTypes.includes(current.type)) {
        // 检查该节点是否有有效的span
        if (this.isValidSpan(current)) {
          return current;
        }
      }

      current = this.getParentNode(current);
      ancestorLevel++;
    }

    return null;
  }

  /**
   * 验证父节点的span（非递归版本，避免循环调用）
   */
  private validateSpanForParent(parentNode: Node): number | null {
    try {
      // 对父节点使用简化的验证策略，避免递归
      if (this.isValidSpan(parentNode)) {
        return (parentNode as NodeWithSpan).span.start;
      }

      // 尝试父节点的策略
      const parentStrategy = getPositionStrategy(parentNode.type);
      if (parentStrategy) {
        const result = parentStrategy.strategy(parentNode);
        if (result !== null) {
          return result;
        }
      }

      return null;
    } catch {
      return null;
    }
  }

  /**
   * 获取当前函数上下文
   */
  private getCurrentFunctionContext(): { name: string; startPosition: number } | undefined {
    const functionStack = this.parentStack.filter(
      (node) =>
        node.type === 'FunctionDeclaration' ||
        node.type === 'FunctionExpression' ||
        node.type === 'ArrowFunctionExpression' ||
        node.type === 'MethodDefinition'
    );

    const currentFunction = functionStack[functionStack.length - 1];
    if (currentFunction) {
      let functionName = 'anonymous';

      if (currentFunction.type === 'FunctionDeclaration') {
        const funcDecl = currentFunction as any;
        functionName = funcDecl.identifier?.value || 'anonymous';
      } else if (currentFunction.type === 'MethodDefinition') {
        const methodDef = currentFunction as any;
        functionName = methodDef.key?.value || 'method';
      }

      const span = (currentFunction as NodeWithSpan).span;
      return {
        name: functionName,
        startPosition: span ? span.start : 0,
      };
    }

    return undefined;
  }

  /**
   * 应用 L1 层位置策略系统
   * 使用策略映射表为节点找到最佳位置，现已增强支持优先级和多级回退
   * @param node AST 节点
   * @returns 策略定位的位置，如果所有策略都失败则返回 null
   */
  private applyPositionStrategy(node: Node): number | null {
    const strategyEntry = getPositionStrategy(node.type);
    if (!strategyEntry) {
      // 没有为此节点类型注册策略，回退到传统逻辑
      return null;
    }

    try {
      // 第一步：尝试主要策略
      const primaryResult = strategyEntry.strategy(node);
      if (primaryResult !== null) {
        this.recordStrategySuccess(node, 'primary', strategyEntry.priority);
        return primaryResult;
      }

      // 第二步：尝试静态回退策略
      if (strategyEntry.fallbackStrategy) {
        const fallbackResult = strategyEntry.fallbackStrategy(node);
        if (fallbackResult !== null) {
          this.recordStrategySuccess(node, 'static-fallback', strategyEntry.priority);
          return fallbackResult;
        }
      }

      // 第三步：尝试基于源代码的实例级回退（最强大的回退）
      const instanceFallback = this.findKeywordPositionInSource(node);
      if (instanceFallback !== null) {
        this.recordStrategySuccess(node, 'enhanced-fallback', strategyEntry.priority);
        return instanceFallback;
      }

      // 第四步：基于优先级的多策略尝试
      const alternativeResult = this.tryAlternativeStrategies(node);
      if (alternativeResult !== null) {
        this.recordStrategySuccess(node, 'alternative-strategy', 0);
        return alternativeResult;
      }
    } catch (error) {
      // 策略执行失败，记录错误并回退
      this.recordStrategyError(node, error as Error);
    }

    return null;
  }

  /**
   * 尝试替代策略
   * 当主策略和回退策略都失败时，尝试其他相关的策略
   * @param node AST 节点
   * @returns 替代策略的结果，如果失败则返回 null
   */
  private tryAlternativeStrategies(node: Node): number | null {
    // 根据节点类型组织相关的替代策略
    const alternativeMap: Record<string, string[]> = {
      DoWhileStatement: ['WhileStatement', 'ForStatement'],
      SwitchStatement: ['IfStatement'],
      TryStatement: ['IfStatement'],
      CatchClause: ['TryStatement'],
      FunctionExpression: ['FunctionDeclaration', 'ArrowFunctionExpression'],
      MethodDefinition: ['FunctionDeclaration', 'FunctionExpression'],
      ConditionalExpression: ['IfStatement'],
      TypeAnnotation: ['TSTypeAnnotation'],
      TSTypeAnnotation: ['TypeAnnotation'],
    };

    const alternatives = alternativeMap[node.type];
    if (!alternatives) {
      return null;
    }

    // 尝试每个替代策略
    for (const altType of alternatives) {
      const altStrategy = getPositionStrategy(altType);
      if (altStrategy) {
        try {
          const result = altStrategy.strategy(node);
          if (result !== null) {
            this.recordAlternativeStrategySuccess(node, altType);
            return result;
          }
        } catch (error) {
          // 忽略替代策略的错误，继续尝试下一个
          console.debug(`Alternative strategy ${altType} failed for ${node.type}:`, error);
        }
      }
    }

    return null;
  }

  /**
   * 记录策略成功
   * @param node 节点
   * @param method 使用的方法
   * @param priority 策略优先级
   */
  private recordStrategySuccess(node: Node, method: string, priority: number): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addStepWithDiagnostic(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'strategy-success',
            description: `策略成功: ${node.type}`,
            context: `方法: ${method}, 优先级: ${priority}`,
          },
          'DEBUG' as any,
          `节点 ${node.type} 使用 ${method} 策略成功定位 (优先级: ${priority})`
        );
      } catch (error) {
        console.warn(`Failed to record strategy success for ${node.type}:`, error);
      }
    }
  }

  /**
   * 记录替代策略成功
   * @param node 节点
   * @param altType 使用的替代策略类型
   */
  private recordAlternativeStrategySuccess(node: Node, altType: string): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addStepWithDiagnostic(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'alternative-strategy-success',
            description: `替代策略成功: ${node.type}`,
            context: `使用策略: ${altType}`,
          },
          'INFO' as any,
          `节点 ${node.type} 使用替代策略 ${altType} 成功定位`
        );
      } catch (error) {
        console.warn(`Failed to record alternative strategy success for ${node.type}:`, error);
      }
    }
  }

  /**
   * 记录策略错误
   * @param node 节点
   * @param error 错误信息
   */
  private recordStrategyError(node: Node, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'strategy-error',
            description: `位置策略执行失败: ${node.type}`,
            context: error.message,
          },
          `节点 ${node.type} 的位置策略执行失败: ${error.message}`
        );
      } catch (detailError) {
        console.warn(`Failed to record strategy error for ${node.type}:`, detailError);
      }
    }
  }

  /**
   * 在源代码中查找节点对应的关键字位置
   * 实例级的回退策略，可以访问源代码进行字符串搜索
   * 现已增强支持所有新的节点类型和专用定位方法
   * @param node AST 节点
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findKeywordPositionInSource(node: Node): number | null {
    // 优先使用专用的实例方法进行精确定位
    switch (node.type) {
      case 'ArrowFunctionExpression':
        return this.findArrowFunctionPosition(node);

      case 'JSXElement':
      case 'JSXFragment':
        return this.findJsxOpeningTagPosition(node);

      case 'JSXExpressionContainer':
        return this.findJSXExpressionContentPosition(node);

      case 'ConditionalExpression':
        return this.findTernaryOperatorPosition(node);

      case 'MethodDefinition':
        return this.findMethodKeywordPosition(node);

      case 'TypeAnnotation':
      case 'TSTypeAnnotation':
        return this.findTypeColonPosition(node);

      case 'MemberExpression':
      case 'MemberExp':
        return this.findMemberAccessPosition(node);

      case 'ComputedMemberExpression':
        return this.findComputedMemberAccessPosition(node);

      case 'OptionalChainingExpression':
        return this.findOptionalChainingPosition(node);

      case 'BlockStatement':
        return this.findBlockOpeningBracePosition(node);
    }

    // 对于其他节点类型，使用通用的关键字查找
    const keywordMap: Record<string, string> = {
      IfStatement: 'if',
      WhileStatement: 'while',
      DoWhileStatement: 'do',
      ForStatement: 'for',
      ForInStatement: 'for',
      ForOfStatement: 'for',
      SwitchStatement: 'switch',
      TryStatement: 'try',
      CatchClause: 'catch',
      FunctionDeclaration: 'function',
      FunctionExpression: 'function',
    };

    const keyword = keywordMap[node.type];
    if (!keyword) {
      return null;
    }

    // 基于父节点确定搜索范围
    const searchRange = this.getSearchRangeForNode(node);
    if (!searchRange) {
      // 如果无法确定搜索范围，使用整个源代码进行查找
      return this.findKeywordInRange(keyword, { start: 0, end: this.sourceCode.length });
    }

    // 在指定范围内查找关键字
    return this.findKeywordInRange(keyword, searchRange);
  }

  /**
   * 为节点确定关键字搜索范围
   * 基于父节点或节点自身的 span 信息来限制搜索范围
   * @param node AST 节点
   * @returns 搜索范围，如果无法确定则返回 null
   */
  private getSearchRangeForNode(node: Node): TokenSearchRange | null {
    // 尝试使用节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number' && typeof nodeWithSpan.span.end === 'number') {
      return {
        start: Math.max(0, nodeWithSpan.span.start - 50), // 向前搜索 50 个字符
        end: Math.min(this.sourceCode.length, nodeWithSpan.span.end + 10), // 向后搜索 10 个字符
      };
    }

    // 尝试使用父节点的 span
    const parent = this.getParent();
    if (parent) {
      const parentWithSpan = parent as any;
      if (
        parentWithSpan.span &&
        typeof parentWithSpan.span.start === 'number' &&
        typeof parentWithSpan.span.end === 'number'
      ) {
        return {
          start: parentWithSpan.span.start,
          end: parentWithSpan.span.end,
        };
      }
    }

    return null;
  }

  /**
   * 记录 span 验证结果
   * @param node 被验证的节点
   * @param span 验证后的 span 位置
   * @param method 验证方法
   * @param success 是否成功
   */
  private recordSpanValidation(node: Node, span: number, method: string, success: boolean): void {
    if (this.detailCollector) {
      try {
        const position = PositionConverter.spanToPosition(this.sourceCode, span);
        const level = success ? (method === 'original' ? 'DEBUG' : 'INFO') : 'WARNING';

        this.detailCollector.addStepWithDiagnostic(
          {
            line: position.line,
            column: position.column,
            increment: 0, // span 验证不增加复杂度
            ruleId: 'span-validation',
            description: `Span验证: ${node.type}`,
            context: `方法: ${method}, 位置: ${position.line}:${position.column}, 状态: ${success ? '成功' : '失败'}`,
          },
          level as any,
          `节点 ${node.type} 的 span 验证${success ? '成功' : '失败'}，使用 ${method} 方法，位置: ${position.line}:${
            position.column
          }`
        );
      } catch (error) {
        // 如果详细信息记录失败，不影响主要流程
        console.warn(`Failed to record span validation for ${node.type}:`, error);
      }
    }
  }

  /**
   * 检查 span 是否有效
   * @param node 要检查的节点
   * @returns 是否有效
   */
  private isValidSpan(node: Node): node is NodeWithSpan {
    const nodeWithSpan = node as NodeWithSpan;
    return (
      nodeWithSpan.span &&
      typeof nodeWithSpan.span.start === 'number' &&
      typeof nodeWithSpan.span.end === 'number' &&
      nodeWithSpan.span.start >= 0 &&
      nodeWithSpan.span.end >= nodeWithSpan.span.start &&
      nodeWithSpan.span.start < this.sourceCode.length
    );
  }

  /**
   * 尝试使用父节点的 span 信息进行回退
   * 这是结构化修正的核心：基于 AST 层次结构而非文本猜测
   * @param node 当前节点
   * @returns 修正后的 span 位置，如果无法修正则返回 null
   */
  private attemptParentSpanFallback(node: Node): number | null {
    // 第一级：检查直接父节点
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      this.recordSpanFallbackStep(node, parent, 'direct-parent');
      return (parent as NodeWithSpan).span.start;
    }

    // 第二级：检查祖父节点
    const grandParent = this.getGrandParent();
    if (grandParent && this.isValidSpan(grandParent)) {
      this.recordSpanFallbackStep(node, grandParent, 'grandparent');
      return (grandParent as NodeWithSpan).span.start;
    }

    // 第三级：向上搜索整个祖先链
    const validAncestor = this.findValidAncestorSpan();
    if (validAncestor !== null) {
      this.recordSpanFallbackStep(node, null, 'ancestor-chain');
      return validAncestor;
    }

    // 第四级：尝试基于节点类型的智能推断
    const inferredSpan = this.inferSpanFromContext(node);
    if (inferredSpan !== null) {
      this.recordSpanFallbackStep(node, null, 'type-inference');
      return inferredSpan;
    }

    return null;
  }

  /**
   * 查找最近的具有有效 span 的祖先节点
   * @returns 有效的 span 位置，如果没有找到则返回 null
   */
  private findValidAncestorSpan(): number | null {
    const parentPath = this.getParentPath();

    // 从最近的祖先开始向上查找
    for (let i = parentPath.length - 1; i >= 0; i--) {
      const ancestor = parentPath[i];
      if (ancestor && this.isValidSpan(ancestor)) {
        return (ancestor as NodeWithSpan).span.start;
      }
    }

    return null;
  }

  /**
   * 基于节点类型和上下文推断 span 位置
   * 当所有父节点都无法提供有效 span 时的最后尝试
   * @param node 要推断的节点
   * @returns 推断的 span 位置，如果无法推断则返回 null
   */
  private inferSpanFromContext(node: Node): number | null {
    // 基于节点类型的启发式推断
    switch (node.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'ForStatement':
      case 'SwitchStatement': {
        // 对于控制流语句，尝试查找相关的关键字位置
        return this.findControlFlowKeywordPosition(node.type);
      }

      case 'FunctionDeclaration':
      case 'MethodDefinition':
      case 'ArrowFunctionExpression': {
        // 对于函数相关节点，尝试找到 'function' 关键字或箭头
        return this.findFunctionKeywordPosition(node.type);
      }

      case 'BlockStatement': {
        // 对于块语句，尝试找到父级控制结构
        const controlParent =
          this.findNearestAncestorOfType('IfStatement') ||
          this.findNearestAncestorOfType('WhileStatement') ||
          this.findNearestAncestorOfType('ForStatement');
        if (controlParent && this.isValidSpan(controlParent)) {
          return (controlParent as NodeWithSpan).span.start;
        }
        break;
      }

      default:
        // 对于其他节点类型，暂不进行推断
        break;
    }

    return null;
  }

  /**
   * 查找控制流关键字的位置（使用智能Token查找系统）
   * @param nodeType 节点类型
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findControlFlowKeywordPosition(nodeType: string): number | null {
    const keywordMap: Record<string, string> = {
      IfStatement: 'if',
      WhileStatement: 'while',
      DoWhileStatement: 'do',
      ForStatement: 'for',
      ForInStatement: 'for',
      ForOfStatement: 'for',
      SwitchStatement: 'switch',
      TryStatement: 'try',
      CatchClause: 'catch',
      ConditionalExpression: '?',
    };

    const keyword = keywordMap[nodeType];
    if (!keyword) return null;

    // 使用Token查找系统进行精确定位
    // 由于这是在推断上下文中调用，我们需要创建一个临时节点或使用当前上下文
    const currentNode = this.getParent() || { type: nodeType, span: null };
    return this.findKeywordPosition(currentNode, keyword);
  }

  /**
   * 查找函数关键字的位置（使用智能Token查找系统）
   * @param nodeType 节点类型
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findFunctionKeywordPosition(nodeType: string): number | null {
    // 创建临时节点来进行查找
    const currentNode = this.getParent() || { type: nodeType, span: null };

    // 根据函数类型查找相应的关键字
    if (nodeType === 'ArrowFunctionExpression') {
      return this.findKeywordPosition(currentNode, '=>');
    }

    return this.findKeywordPosition(currentNode, 'function');
  }

  /**
   * SWC Token 查找系统 - 精确定位关键字位置
   *
   * 实现三级降级策略：
   * 1. SWC AST Token 查找（最精确）
   * 2. 字符串模式匹配（备用）
   * 3. indexOf 查找（兜底）
   *
   * @param node AST 节点
   * @param keyword 要查找的关键字
   * @returns 关键字的字节偏移位置，如果未找到则返回 null
   */
  public findKeywordPosition(node: any, keyword: string): number | null {
    // 获取节点的搜索范围（如果有有效span则限制搜索范围）
    const searchRange = this.getSearchRange(node);

    try {
      // 策略1: SWC AST Token 查找
      const tokenPosition = this.findKeywordByTokenAnalysis(keyword, searchRange);
      if (tokenPosition !== null) {
        this.recordTokenSearchResult(node, keyword, tokenPosition, 'token-analysis');
        return tokenPosition;
      }

      // 策略2: 智能字符串模式匹配
      const patternPosition = this.findKeywordByPatternMatching(keyword, searchRange);
      if (patternPosition !== null) {
        this.recordTokenSearchResult(node, keyword, patternPosition, 'pattern-matching');
        return patternPosition;
      }

      // 策略3: 简单 indexOf 查找（兜底策略）
      const indexPosition = this.findKeywordByIndexOf(keyword, searchRange);
      if (indexPosition !== null) {
        this.recordTokenSearchResult(node, keyword, indexPosition, 'index-fallback');
        return indexPosition;
      }

      // 所有策略都失败
      this.recordTokenSearchResult(node, keyword, null, 'all-failed');
      return null;
    } catch (error) {
      this.recordTokenSearchError(node, keyword, error as Error);
      return null;
    }
  }

  /**
   * 获取节点的搜索范围
   * @param node AST 节点
   * @returns 搜索范围 { start, end }
   */
  private getSearchRange(node: any): { start: number; end: number } {
    // 如果节点有有效的 span，使用它来限制搜索范围
    if (this.isValidSpan(node)) {
      const span = (node as NodeWithSpan).span;
      // 扩展搜索范围以包含可能的前置空白和关键字
      const expandedStart = Math.max(0, span.start - 50);
      const expandedEnd = Math.min(this.sourceCode.length, span.end + 10);
      return { start: expandedStart, end: expandedEnd };
    }

    // 如果没有有效span，尝试使用父节点范围
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      const parentSpan = (parent as NodeWithSpan).span;
      return { start: parentSpan.start, end: parentSpan.end };
    }

    // 最后回退到整个源代码
    return { start: 0, end: this.sourceCode.length };
  }

  /**
   * 策略1: 使用 SWC AST 和 Token 分析查找关键字
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByTokenAnalysis(keyword: string, range: { start: number; end: number }): number | null {
    try {
      // 由于 @swc/core v1.13.2 没有直接的 tokenize API，
      // 我们使用 AST 结合智能搜索的方式来实现"Token查找"效果

      // 提取搜索范围内的代码片段
      const searchCode = this.sourceCode.slice(range.start, range.end);

      // 使用正则表达式进行词边界匹配（模拟Token级别的精确性）
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g');
      let match;
      const candidates: number[] = [];

      while ((match = keywordRegex.exec(searchCode)) !== null) {
        const absolutePosition = range.start + match.index;
        candidates.push(absolutePosition);
      }

      // 如果找到候选位置，选择最合适的一个
      if (candidates.length > 0) {
        // 对于多个匹配，选择第一个（通常是节点开始处的关键字）
        const firstCandidate = candidates[0];
        return firstCandidate !== undefined ? firstCandidate : null;
      }

      return null;
    } catch (error) {
      console.debug('Token analysis failed:', error);
      return null;
    }
  }

  /**
   * 策略2: 智能字符串模式匹配
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByPatternMatching(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);

      // 创建多种可能的匹配模式
      const patterns = [
        // 标准模式：关键字后跟空白或括号
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\s*[\\(\\{\\s]`, 'g'),
        // 行首模式：行首的关键字
        new RegExp(`^\\s*${this.escapeRegExp(keyword)}\\b`, 'gm'),
        // 一般模式：词边界匹配
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g'),
      ];

      for (const pattern of patterns) {
        pattern.lastIndex = 0; // 重置正则表达式状态
        const match = pattern.exec(searchCode);
        if (match) {
          // 找到匹配的关键字本身的位置（不包括后续的空白或括号）
          const keywordStart = match.index;
          const actualKeywordMatch = match[0].match(new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`));
          if (actualKeywordMatch) {
            const keywordOffset = match[0].indexOf(actualKeywordMatch[0]);
            return range.start + keywordStart + keywordOffset;
          }
        }
      }

      return null;
    } catch (error) {
      console.debug('Pattern matching failed:', error);
      return null;
    }
  }

  /**
   * 策略3: 简单 indexOf 查找（兜底策略）
   * @param keyword 关键字
   * @param range 搜索范围
   * @returns 关键字位置或 null
   */
  private findKeywordByIndexOf(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf(keyword);

      if (index >= 0) {
        return range.start + index;
      }

      return null;
    } catch (error) {
      console.debug('indexOf search failed:', error);
      return null;
    }
  }

  /**
   * 转义正则表达式中的特殊字符
   * @param string 要转义的字符串
   * @returns 转义后的字符串
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 记录Token搜索结果
   * @param node 节点
   * @param keyword 关键字
   * @param position 找到的位置
   * @param method 使用的方法
   */
  private recordTokenSearchResult(node: any, keyword: string, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'token-search',
            description: `Token查找: ${keyword}`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'} '${keyword}' 关键字${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record token search result for ${keyword}:`, error);
      }
    }
  }

  /**
   * 记录Token搜索错误
   * @param node 节点
   * @param keyword 关键字
   * @param error 错误信息
   */
  private recordTokenSearchError(node: any, keyword: string, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'token-search-error',
            description: `Token查找失败: ${keyword}`,
            context: error.message,
          },
          `Token查找系统在处理 '${keyword}' 时发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record token search error for ${keyword}:`, detailError);
      }
    }
  }

  // =============================================================================
  // JSX 元素精确定位系统 - 处理各种 JSX 场景的位置定位
  // =============================================================================

  /**
   * 查找 JSX 开放标签位置
   *
   * 支持多种 JSX 场景：
   * - 简单元素：<div>content</div>
   * - 自闭合元素：<img src="..." />
   * - 带属性元素：<button onClick={handler}>Click</button>
   * - 组件：<MyComponent prop={value} />
   * - Fragment：<>content</>
   *
   * @param node JSX 节点
   * @returns JSX 开放标签的字节偏移位置，如果未找到则返回 null
   */
  public findJsxOpeningTagPosition(node: any): number | null {
    try {
      // 验证节点类型
      if (!this.isJSXElementNode(node)) {
        return null;
      }

      // 获取搜索范围
      const searchRange = this.getSearchRange(node);

      // 策略1: 基于节点类型的精确查找
      const tagPosition = this.findJSXTagByNodeType(node, searchRange);
      if (tagPosition !== null) {
        this.recordJSXSearchResult(node, 'opening-tag', tagPosition, 'node-type-analysis');
        return tagPosition;
      }

      // 策略2: 基于标签名的查找
      const namePosition = this.findJSXTagByName(node, searchRange);
      if (namePosition !== null) {
        this.recordJSXSearchResult(node, 'opening-tag', namePosition, 'tag-name-search');
        return namePosition;
      }

      // 策略3: 通用 '<' 符号查找
      const bracketPosition = this.findJSXOpeningBracket(searchRange);
      if (bracketPosition !== null) {
        this.recordJSXSearchResult(node, 'opening-tag', bracketPosition, 'bracket-search');
        return bracketPosition;
      }

      // 所有策略都失败
      this.recordJSXSearchResult(node, 'opening-tag', null, 'all-failed');
      return null;
    } catch (error) {
      this.recordJSXSearchError(node, 'opening-tag', error as Error);
      return null;
    }
  }

  /**
   * 查找 JSX 表达式内容位置
   *
   * 支持各种表达式场景：
   * - 条件表达式：{condition ? <A /> : <B />}
   * - 变量插值：{value}
   * - 函数调用：{getData()}
   * - 复杂表达式：{items.map(item => <Item key={item.id} />)}
   *
   * @param node JSX 表达式容器节点
   * @returns JSX 表达式内容的字节偏移位置，如果未找到则返回 null
   */
  public findJSXExpressionContentPosition(node: any): number | null {
    try {
      // 验证节点类型
      if (!this.isJSXExpressionNode(node)) {
        return null;
      }

      // 获取搜索范围
      const searchRange = this.getSearchRange(node);

      // 策略1: 查找开大括号后的内容
      const contentPosition = this.findJSXExpressionContent(node, searchRange);
      if (contentPosition !== null) {
        this.recordJSXSearchResult(node, 'expression-content', contentPosition, 'content-analysis');
        return contentPosition;
      }

      // 策略2: 基于表达式内容类型的查找
      const expressionPosition = this.findJSXExpressionByType(node, searchRange);
      if (expressionPosition !== null) {
        this.recordJSXSearchResult(node, 'expression-content', expressionPosition, 'expression-type-search');
        return expressionPosition;
      }

      // 策略3: 通用大括号查找
      const bracePosition = this.findJSXOpeningBrace(searchRange);
      if (bracePosition !== null) {
        // 返回大括号后第一个非空白字符的位置
        const contentStart = this.findFirstNonWhitespaceAfter(bracePosition + 1, searchRange);
        if (contentStart !== null) {
          this.recordJSXSearchResult(node, 'expression-content', contentStart, 'brace-search');
          return contentStart;
        }
      }

      // 所有策略都失败
      this.recordJSXSearchResult(node, 'expression-content', null, 'all-failed');
      return null;
    } catch (error) {
      this.recordJSXSearchError(node, 'expression-content', error as Error);
      return null;
    }
  }

  /**
   * 检查节点是否为 JSX 元素节点
   * @param node 节点
   * @returns 是否为 JSX 元素节点
   */
  private isJSXElementNode(node: any): boolean {
    if (!node || !node.type) return false;

    return node.type === 'JSXElement' || node.type === 'JSXFragment';
  }

  /**
   * 检查节点是否为 JSX 表达式节点
   * @param node 节点
   * @returns 是否为 JSX 表达式节点
   */
  private isJSXExpressionNode(node: any): boolean {
    if (!node || !node.type) return false;

    return node.type === 'JSXExpressionContainer';
  }

  /**
   * 基于节点类型查找 JSX 标签
   * @param node JSX 节点
   * @param range 搜索范围
   * @returns 标签位置，如果未找到则返回 null
   */
  private findJSXTagByNodeType(node: any, range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);

      switch (node.type) {
        case 'JSXFragment':
          // Fragment: <> 或 <React.Fragment>
          const fragmentMatch = searchCode.match(/(<>|<React\.Fragment[\s>])/);
          if (fragmentMatch) {
            return range.start + fragmentMatch.index!;
          }
          break;

        case 'JSXElement':
        case 'JSXSelfClosingElement':
          // 普通元素或自闭合元素
          const elementMatch = searchCode.match(/<[A-Za-z][A-Za-z0-9]*|<[A-Z][A-Za-z0-9]*\./);
          if (elementMatch) {
            return range.start + elementMatch.index!;
          }
          break;
      }

      return null;
    } catch (error) {
      console.debug('JSX node type analysis failed:', error);
      return null;
    }
  }

  /**
   * 基于标签名查找 JSX 标签
   * @param node JSX 节点
   * @param range 搜索范围
   * @returns 标签位置，如果未找到则返回 null
   */
  private findJSXTagByName(node: any, range: { start: number; end: number }): number | null {
    try {
      // 尝试提取标签名
      const tagName = this.extractJSXTagName(node);
      if (!tagName) return null;

      const searchCode = this.sourceCode.slice(range.start, range.end);

      // 使用标签名构建精确的正则表达式
      const tagPattern = new RegExp(`<${this.escapeRegExp(tagName)}(?:[\\s/>]|$)`, 'g');
      const match = tagPattern.exec(searchCode);

      if (match) {
        return range.start + match.index;
      }

      return null;
    } catch (error) {
      console.debug('JSX tag name search failed:', error);
      return null;
    }
  }

  /**
   * 提取 JSX 标签名
   * @param node JSX 节点
   * @returns 标签名，如果无法提取则返回 null
   */
  private extractJSXTagName(node: any): string | null {
    try {
      if (node.type === 'JSXFragment') {
        return ''; // Fragment 没有具体的标签名
      }

      // 对于 JSXElement，标签名在 opening.name 中
      const openingElement = node.opening;
      if (!openingElement || !openingElement.name) return null;

      const name = openingElement.name;

      // 处理不同类型的标签名
      if (name.type === 'Identifier') {
        return name.value || null;
      } else if (name.type === 'JSXMemberExpression') {
        // 处理 <MyComponent.SubComponent> 形式
        const object = name.object;
        const property = name.property;
        if (object && property) {
          const objectName = object.value;
          const propertyName = property.value;
          return objectName && propertyName ? `${objectName}.${propertyName}` : null;
        }
      } else if (name.type === 'JSXNamespacedName') {
        // 处理 <ns:element> 形式
        const namespace = name.namespace;
        const localName = name.name;
        if (namespace && localName) {
          const nsName = namespace.value;
          const localValue = localName.value;
          return nsName && localValue ? `${nsName}:${localValue}` : null;
        }
      }

      return null;
    } catch (error) {
      console.debug('JSX tag name extraction failed:', error);
      return null;
    }
  }

  /**
   * 查找 JSX 开放括号 '<'
   * @param range 搜索范围
   * @returns 括号位置，如果未找到则返回 null
   */
  private findJSXOpeningBracket(range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf('<');

      if (index >= 0) {
        return range.start + index;
      }

      return null;
    } catch (error) {
      console.debug('JSX opening bracket search failed:', error);
      return null;
    }
  }

  /**
   * 查找 JSX 表达式内容
   * @param node JSX 表达式节点
   * @param range 搜索范围
   * @returns 内容位置，如果未找到则返回 null
   */
  private findJSXExpressionContent(node: any, range: { start: number; end: number }): number | null {
    try {
      // 尝试从表达式节点中获取内容
      const expression = node.expression;
      if (expression && this.isValidSpan(expression)) {
        const expressionSpan = (expression as NodeWithSpan).span;
        if (expressionSpan.start >= range.start && expressionSpan.start <= range.end) {
          return expressionSpan.start;
        }
      }

      // 如果表达式有span信息但不在范围内，尝试在源代码中查找
      if (expression) {
        const expressionContent = this.extractExpressionContent(expression);
        if (expressionContent) {
          const searchCode = this.sourceCode.slice(range.start, range.end);
          const contentIndex = searchCode.indexOf(expressionContent);
          if (contentIndex >= 0) {
            return range.start + contentIndex;
          }
        }
      }

      return null;
    } catch (error) {
      console.debug('JSX expression content search failed:', error);
      return null;
    }
  }

  /**
   * 提取表达式的文本内容
   * @param expression 表达式节点
   * @returns 表达式文本内容，如果无法提取则返回 null
   */
  private extractExpressionContent(expression: any): string | null {
    try {
      switch (expression.type) {
        case 'Identifier':
          return expression.value || expression.name || null;

        case 'CallExpression':
          // 对于函数调用，尝试获取被调用者的内容
          const callee = expression.callee;
          if (callee) {
            // 如果是成员表达式调用，如 items.map()
            if (callee.type === 'MemberExpression') {
              return this.extractExpressionContent(callee);
            } else if (callee.type === 'Identifier') {
              return callee.value || callee.name || null;
            }
          }
          break;

        case 'MemberExpression':
          // 对于成员表达式，构建完整的访问路径
          const object = expression.object;
          const property = expression.property;
          if (object && property) {
            const objectName = this.extractExpressionContent(object) || object.value || object.name;
            const propertyName = property.value || property.name;
            if (objectName && propertyName) {
              return `${objectName}.${propertyName}`;
            } else if (objectName) {
              return objectName; // 至少返回对象名
            }
          }
          break;

        case 'ConditionalExpression':
          // 对于条件表达式，返回测试条件
          const test = expression.test;
          if (test) {
            return this.extractExpressionContent(test) || test.value || test.name;
          }
          break;

        case 'BinaryExpression':
        case 'LogicalExpression':
          // 对于二元表达式，返回左操作数
          const left = expression.left;
          if (left) {
            return this.extractExpressionContent(left) || left.value || left.name;
          }
          break;
      }

      return null;
    } catch (error) {
      console.debug('Expression content extraction failed:', error);
      return null;
    }
  }

  /**
   * 基于表达式类型查找 JSX 表达式
   * @param node JSX 表达式节点
   * @param range 搜索范围
   * @returns 表达式位置，如果未找到则返回 null
   */
  private findJSXExpressionByType(node: any, range: { start: number; end: number }): number | null {
    try {
      const expression = node.expression;
      if (!expression) return null;

      const searchCode = this.sourceCode.slice(range.start, range.end);

      // 先尝试提取表达式内容进行精确匹配
      const expressionContent = this.extractExpressionContent(expression);
      if (expressionContent) {
        // 查找第一个匹配的表达式内容
        const contentIndex = searchCode.indexOf(expressionContent);
        if (contentIndex >= 0) {
          return range.start + contentIndex;
        }
      }

      // 根据表达式类型使用不同的查找策略作为回退
      switch (expression.type) {
        case 'ConditionalExpression':
          // 查找三元运算符
          const ternaryMatch = searchCode.match(/\w+\s*\?\s*/);
          if (ternaryMatch) {
            return range.start + ternaryMatch.index!;
          }
          break;

        case 'CallExpression':
          // 查找函数调用
          const callMatch = searchCode.match(/\w+\s*\(/);
          if (callMatch) {
            return range.start + callMatch.index!;
          }
          break;

        case 'MemberExpression':
          // 查找成员访问
          const memberMatch = searchCode.match(/\w+\.\w+/);
          if (memberMatch) {
            return range.start + memberMatch.index!;
          }
          break;

        case 'Identifier':
          // 查找标识符
          const identifier = expression.value || expression.name;
          if (identifier) {
            const identifierMatch = searchCode.match(new RegExp(`\\b${this.escapeRegExp(identifier)}\\b`));
            if (identifierMatch) {
              return range.start + identifierMatch.index!;
            }
          }
          break;
      }

      return null;
    } catch (error) {
      console.debug('JSX expression type search failed:', error);
      return null;
    }
  }

  /**
   * 查找 JSX 开放大括号 '{'
   * @param range 搜索范围
   * @returns 大括号位置，如果未找到则返回 null
   */
  private findJSXOpeningBrace(range: { start: number; end: number }): number | null {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf('{');

      if (index >= 0) {
        return range.start + index;
      }

      return null;
    } catch (error) {
      console.debug('JSX opening brace search failed:', error);
      return null;
    }
  }

  /**
   * 查找指定位置后的第一个非空白字符
   * @param position 起始位置
   * @param range 搜索范围
   * @returns 第一个非空白字符位置，如果未找到则返回 null
   */
  private findFirstNonWhitespaceAfter(position: number, range: { start: number; end: number }): number | null {
    try {
      if (position < range.start || position >= range.end) {
        return null;
      }

      for (let i = position; i < range.end; i++) {
        const char = this.sourceCode[i];
        if (char && char.trim().length > 0) {
          return i;
        }
      }

      return null;
    } catch (error) {
      console.debug('First non-whitespace search failed:', error);
      return null;
    }
  }

  /**
   * 记录 JSX 搜索结果
   * @param node 节点
   * @param searchType 搜索类型
   * @param position 找到的位置
   * @param method 使用的方法
   */
  private recordJSXSearchResult(node: any, searchType: string, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'jsx-search',
            description: `JSX ${searchType}查找`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'} JSX ${searchType}${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record JSX search result for ${searchType}:`, error);
      }
    }
  }

  /**
   * 记录 JSX 搜索错误
   * @param node 节点
   * @param searchType 搜索类型
   * @param error 错误信息
   */
  private recordJSXSearchError(node: any, searchType: string, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'jsx-search-error',
            description: `JSX ${searchType}查找失败`,
            context: error.message,
          },
          `JSX ${searchType}查找系统在处理 ${node.type} 时发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record JSX search error for ${searchType}:`, detailError);
      }
    }
  }

  /**
   * 记录 span 回退步骤
   * @param node 被修正的节点
   * @param fallbackNode 提供回退 span 的节点
   * @param method 回退方法
   */
  private recordSpanFallbackStep(node: Node, fallbackNode: Node | null, method: string): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addStepWithDiagnostic(
          {
            line: 0, // 将在记录时通过 span 转换得到
            column: 0,
            increment: 0,
            ruleId: 'span-fallback',
            description: `Span回退: ${node.type}`,
            context: `方法: ${method}, 来源: ${fallbackNode?.type || 'unknown'}`,
          },
          'INFO' as any,
          `节点 ${node.type} 使用 ${method} 方法进行 span 回退${fallbackNode ? `，基于 ${fallbackNode.type} 节点` : ''}`
        );
      } catch (error) {
        console.warn(`Failed to record span fallback for ${node.type}:`, error);
      }
    }
  }

  /**
   * 获取默认 span 位置
   * 当所有修正策略都失败时使用
   * @returns 默认的字节偏移位置
   */
  private getDefaultSpan(): number {
    // 多级降级策略，确保总有有效位置返回

    // 第一级：尝试找到当前函数的开始位置
    const functionStart = this.findCurrentFunctionStart();
    if (functionStart !== null) {
      return functionStart;
    }

    // 第二级：尝试找到文件中第一个有效的代码位置
    const firstCodePosition = this.findFirstCodePosition();
    if (firstCodePosition !== null) {
      return firstCodePosition;
    }

    // 第三级：返回文件开始位置（绝对保底）
    return 0;
  }

  /**
   * 查找当前函数的开始位置
   * @returns 函数开始位置，如果未找到则返回 null
   */
  private findCurrentFunctionStart(): number | null {
    // 向上查找函数声明节点
    const functionNode =
      this.findNearestAncestorOfType('FunctionDeclaration') ||
      this.findNearestAncestorOfType('MethodDefinition') ||
      this.findNearestAncestorOfType('ArrowFunctionExpression') ||
      this.findNearestAncestorOfType('FunctionExpression');

    if (functionNode && this.isValidSpan(functionNode)) {
      return (functionNode as NodeWithSpan).span.start;
    }

    return null;
  }

  /**
   * 查找文件中第一个有效的代码位置
   * 跳过注释、空白行等
   * @returns 第一个代码位置，如果未找到则返回 null
   */
  private findFirstCodePosition(): number | null {
    const lines = this.sourceCode.split('\n');
    let offset = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 跳过空行和注释行
      if (
        trimmedLine.length > 0 &&
        !trimmedLine.startsWith('//') &&
        !trimmedLine.startsWith('/*') &&
        !trimmedLine.startsWith('*')
      ) {
        // 找到第一个非空白字符的位置
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }

      offset += line.length + 1; // +1 for newline character
    }

    return null;
  }

  /**
   * 记录 span 修正错误
   * @param node 节点
   * @param error 错误信息
   */
  private recordSpanError(node: Node, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'span-error',
            description: `Span修正失败: ${node.type}`,
            context: error.message,
          },
          `无法修正节点 ${node.type} 的 span: ${error.message}`
        );
      } catch (detailError) {
        // 双重错误，只记录到控制台
        console.error(`Failed to record span error for ${node.type}:`, detailError);
      }
    }
  }

  /**
   * 处理节点访问过程中的错误
   * @param node 出错的节点
   * @param error 错误信息
   */
  private handleVisitError(node: Node, error: Error): void {
    console.warn(`Error visiting node ${node.type}:`, error.message);

    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'visit-error',
            description: `访问节点失败: ${node.type}`,
            context: error.message,
          },
          `节点 ${node.type} 访问过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record visit error for ${node.type}:`, detailError);
      }
    }
  }

  /**
   * 增加复杂度
   * 内部辅助方法，用于在具体的节点访问方法中增加复杂度
   * @param increment 复杂度增量
   * @param node 相关节点
   * @param ruleId 规则标识符
   * @param description 规则描述
   * @param context 可选的上下文信息
   */
  protected addComplexity(increment: number, node: Node, ruleId: string, description: string, context?: string): void {
    this.totalComplexity += increment;

    if (this.detailCollector && increment > 0) {
      try {
        const validSpan = this.validateSpan(node);
        const position = PositionConverter.spanToPosition(this.sourceCode, validSpan);

        this.detailCollector.addStep({
          line: position.line,
          column: position.column,
          increment,
          ruleId,
          description,
          context: context || `嵌套层级: ${this.nestingLevel}`,
        });
      } catch (error) {
        // 如果详细信息记录失败，复杂度仍然需要计算
        console.warn(`Failed to record complexity step for ${ruleId}:`, error);
      }
    }
  }

  /**
   * 进入嵌套上下文
   * 在访问嵌套结构（如 if、for 等）时调用
   */
  protected enterNesting(): void {
    this.nestingLevel++;
  }

  /**
   * 退出嵌套上下文
   * 在离开嵌套结构时调用
   */
  protected exitNesting(): void {
    this.nestingLevel = Math.max(0, this.nestingLevel - 1);
  }

  /**
   * 获取当前嵌套层级的复杂度增量
   * 根据嵌套深度计算额外的复杂度
   * @returns 嵌套复杂度增量
   */
  protected getNestingIncrement(): number {
    return this.nestingLevel;
  }

  // =============================================================================
  // 箭头函数定位实例方法 - Task 6 完整实现
  // =============================================================================

  /**
   * 实例方法：查找箭头函数位置
   * 可以访问源代码进行精确定位，支持各种箭头函数形式
   *
   * 支持的箭头函数场景：
   * - 单参数箭头函数：`x => x * 2`
   * - 多参数箭头函数：`(a, b) => a + b`
   * - 无参数箭头函数：`() => console.log('hello')`
   * - 类型注解箭头函数：`(x: number): number => x * 2`
   * - 异步箭头函数：`async (x) => await fetch(x)`
   * - 复杂表达式：`items.filter(item => item.active).map(item => item.name)`
   * - 嵌套箭头函数：`x => y => x + y`
   * - 对象解构：`({name, age}) => ({name, age, id: Math.random()})`
   * - 数组解构：`([first, ...rest]) => first`
   *
   * @param node 箭头函数节点
   * @returns 箭头函数的最佳位置
   */
  public findArrowFunctionPosition(node: any): number | null {
    if (!node || node.type !== 'ArrowFunctionExpression') {
      return null;
    }

    try {
      // 策略1：精确查找箭头符号 '=>'
      const arrowPosition = this.findArrowOperatorInSource(node);
      if (arrowPosition !== null) {
        this.recordArrowFunctionPositionResult(node, arrowPosition, 'arrow-operator');
        return arrowPosition;
      }

      // 策略2：定位到参数列表开始（处理各种参数形式）
      const paramPosition = this.findParameterListStart(node);
      if (paramPosition !== null) {
        this.recordArrowFunctionPositionResult(node, paramPosition, 'parameter-list');
        return paramPosition;
      }

      // 策略3：基于函数体推断箭头位置
      const bodyBasedPosition = this.findArrowPositionFromBody(node);
      if (bodyBasedPosition !== null) {
        this.recordArrowFunctionPositionResult(node, bodyBasedPosition, 'body-inference');
        return bodyBasedPosition;
      }

      // 策略4：使用节点 span 作为最后回退
      if (node.span && typeof node.span.start === 'number') {
        this.recordArrowFunctionPositionResult(node, node.span.start, 'span-fallback');
        return node.span.start;
      }

      // 所有策略都失败
      this.recordArrowFunctionPositionResult(node, null, 'all-failed');
      return null;
    } catch (error) {
      // 错误处理：记录错误但不中断流程
      this.recordArrowFunctionPositionError(node, error as Error);
      return null;
    }
  }

  /**
   * 在源代码中查找箭头操作符的位置
   * 支持各种箭头函数形式的精确定位
   *
   * @param node 箭头函数节点
   * @returns 箭头操作符位置
   */
  private findArrowOperatorInSource(node: any): number | null {
    // 确定搜索范围
    const searchRange = this.getArrowFunctionSearchRange(node);
    if (!searchRange) {
      return null;
    }

    // 在范围内搜索箭头符号
    const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);

    // 使用多种策略匹配箭头符号，避免误匹配
    const arrowPatterns = [
      // 策略1：标准箭头符号（前后可能有空白）
      /\s*=>\s*/g,
      // 策略2：紧凑格式的箭头符号
      /=>/g,
      // 策略3：带有类型注解的箭头符号
      /\):\s*[A-Za-z<>[\]{}|&\s]+\s*=>\s*/g,
    ];

    for (const pattern of arrowPatterns) {
      pattern.lastIndex = 0; // 重置正则状态
      let match;
      while ((match = pattern.exec(searchText)) !== null) {
        // 找到 '=>' 在匹配文本中的确切位置
        const arrowIndex = match[0].indexOf('=>');
        if (arrowIndex >= 0) {
          const absolutePosition = searchRange.start + match.index + arrowIndex;
          // 验证这是一个真正的箭头函数箭头，而不是其他上下文中的
          if (this.validateArrowOperatorContext(absolutePosition, node)) {
            return absolutePosition;
          }
        }
      }
    }

    return null;
  }

  /**
   * 验证箭头操作符的上下文是否正确
   * 确保找到的 '=>' 确实属于当前箭头函数
   *
   * @param position 箭头符号位置
   * @param node 箭头函数节点
   * @returns 是否是有效的箭头操作符
   */
  private validateArrowOperatorContext(position: number, node: any): boolean {
    // 基本验证：位置必须在节点span范围内
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      if (position < node.span.start || position >= node.span.end) {
        return false;
      }
    }

    // 检查箭头前是否有参数或参数列表
    const beforeArrow = this.sourceCode.slice(Math.max(0, position - 50), position);

    // 常见的有效模式：
    // - 单参数：x => 或 param =>
    // - 多参数：(a, b) => 或 (x: number, y: string) =>
    // - 无参数：() =>
    // - 异步：async (x) => 或 async x =>
    const validPatterns = [
      /\w\s*$/, // 单参数
      /\)\s*$/, // 多参数或无参数的右括号
      /\]\s*$/, // 数组解构参数
      /\}\s*$/, // 对象解构参数
      /async\s+\w\s*$/, // async 单参数
      /async\s*\(\s*\w*\s*\)\s*$/, // async 括号参数
    ];

    return validPatterns.some((pattern) => pattern.test(beforeArrow));
  }

  /**
   * 确定箭头函数的搜索范围
   * 基于参数列表和函数体的位置
   *
   * @param node 箭头函数节点
   * @returns 搜索范围
   */
  private getArrowFunctionSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 确定搜索起始位置
    if (node.params && node.params.length > 0) {
      // 从最后一个参数结束后开始
      const lastParam = node.params[node.params.length - 1];
      if (lastParam && lastParam.span && typeof lastParam.span.end === 'number') {
        searchStart = lastParam.span.end;
      }
    } else if (node.span && typeof node.span.start === 'number') {
      // 无参数情况，从函数开始
      searchStart = node.span.start;
    }

    // 确定搜索结束位置
    if (node.body && node.body.span && typeof node.body.span.start === 'number') {
      searchEnd = node.body.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 查找参数列表的开始位置
   * 处理有括号和无括号的参数列表，以及各种解构和类型注解形式
   *
   * 支持的参数形式：
   * - 单参数：x、param、_
   * - 多参数：(a, b)、(x: number, y: string)
   * - 无参数：()
   * - 对象解构：({name, age})、({name: n, age: a})
   * - 数组解构：([first, second])、([first, ...rest])
   * - 混合解构：({items: [first, second]})
   * - 类型注解：(x: number)、({name}: {name: string})
   * - 默认参数：(x = 10)、({name = 'default'})
   * - 异步参数：async (x)、async ({data})
   *
   * @param node 箭头函数节点
   * @returns 参数列表开始位置
   */
  private findParameterListStart(node: any): number | null {
    if (!node.params) {
      return null;
    }

    try {
      // 情况1：有参数
      if (node.params.length > 0) {
        const firstParam = node.params[0];
        if (firstParam && firstParam.span && typeof firstParam.span.start === 'number') {
          const paramStart = firstParam.span.start;

          // 检查参数前是否有括号、async关键字等
          const parameterContext = this.analyzeParameterContext(paramStart, node);

          // 根据上下文确定最佳起始位置
          if (parameterContext.hasParentheses) {
            return parameterContext.openParenPosition || paramStart;
          } else if (parameterContext.hasAsyncKeyword) {
            return parameterContext.asyncKeywordPosition || paramStart;
          } else {
            // 单参数无括号情况，返回参数本身的位置
            return paramStart;
          }
        }
      }

      // 情况2：无参数 () => ...
      if (node.params.length === 0 && node.span && typeof node.span.start === 'number') {
        // 查找空参数列表的开括号
        return this.findEmptyParameterListStart(node.span.start);
      }

      return null;
    } catch (error) {
      console.debug('Parameter list start detection failed:', error);
      return null;
    }
  }

  /**
   * 分析参数的上下文，检测括号、async 关键字等
   *
   * @param paramStart 参数起始位置
   * @param node 箭头函数节点
   * @returns 参数上下文分析结果
   */
  private analyzeParameterContext(
    paramStart: number,
    node: any
  ): {
    hasParentheses: boolean;
    openParenPosition: number | null;
    hasAsyncKeyword: boolean;
    asyncKeywordPosition: number | null;
    isDestructuring: boolean;
  } {
    const result = {
      hasParentheses: false,
      openParenPosition: null,
      hasAsyncKeyword: false,
      asyncKeywordPosition: null,
      isDestructuring: false,
    };

    // 向前查找最多 50 个字符来分析上下文
    const searchStart = Math.max(0, paramStart - 50);
    const contextText = this.sourceCode.slice(searchStart, paramStart);

    // 检查括号
    const openParenIndex = contextText.lastIndexOf('(');
    if (openParenIndex >= 0) {
      result.hasParentheses = true;
      result.openParenPosition = searchStart + openParenIndex;
    }

    // 检查 async 关键字
    const asyncMatch = contextText.match(/\basync\s*$/);
    if (asyncMatch) {
      result.hasAsyncKeyword = true;
      result.asyncKeywordPosition = searchStart + asyncMatch.index!;
    }

    // 检查解构模式
    const destructuringPatterns = ['{', '['];
    result.isDestructuring = destructuringPatterns.some(
      (pattern) => contextText.includes(pattern) && contextText.lastIndexOf(pattern) > Math.max(openParenIndex, -1)
    );

    return result;
  }

  /**
   * 检查指定位置前是否有开括号
   *
   * @param position 参数起始位置
   * @returns 是否有开括号
   */
  private hasOpeningParenthesis(position: number): boolean {
    // 向前查找最多10个字符，寻找开括号
    const searchStart = Math.max(0, position - 10);
    const searchText = this.sourceCode.slice(searchStart, position);
    return searchText.includes('(');
  }

  /**
   * 查找开括号的位置
   *
   * @param paramStart 参数起始位置
   * @returns 开括号位置
   */
  private findOpeningParenthesis(paramStart: number): number | null {
    const searchStart = Math.max(0, paramStart - 20);
    const searchText = this.sourceCode.slice(searchStart, paramStart);
    const parenIndex = searchText.lastIndexOf('(');

    return parenIndex >= 0 ? searchStart + parenIndex : null;
  }

  /**
   * 基于函数体推断箭头位置
   * 当无法通过参数定位时，基于函数体的位置反向推断箭头位置
   *
   * @param node 箭头函数节点
   * @returns 推断的箭头位置
   */
  private findArrowPositionFromBody(node: any): number | null {
    if (!node.body || !node.body.span || typeof node.body.span.start !== 'number') {
      return null;
    }

    const bodyStart = node.body.span.start;

    // 在函数体开始前查找箭头符号
    const searchStart = Math.max(0, bodyStart - 100); // 向前搜索最多100个字符
    const searchText = this.sourceCode.slice(searchStart, bodyStart);

    // 从后往前查找最后一个箭头符号
    const arrowRegex = /=>/g;
    let lastMatch = null;
    let match;

    while ((match = arrowRegex.exec(searchText)) !== null) {
      lastMatch = match;
    }

    if (lastMatch) {
      const arrowPosition = searchStart + lastMatch.index;

      // 验证这个箭头符号在合理的位置
      if (this.validateArrowPositionFromBody(arrowPosition, node)) {
        return arrowPosition;
      }
    }

    return null;
  }

  /**
   * 验证基于函数体推断的箭头位置是否合理
   *
   * @param arrowPosition 推断的箭头位置
   * @param node 箭头函数节点
   * @returns 是否是合理的箭头位置
   */
  private validateArrowPositionFromBody(arrowPosition: number, node: any): boolean {
    // 基本检查：箭头位置应该在节点 span 范围内
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      if (arrowPosition < node.span.start || arrowPosition >= node.span.end) {
        return false;
      }
    }

    // 检查箭头前的内容是否像参数列表
    const beforeArrow = this.sourceCode.slice(Math.max(0, arrowPosition - 30), arrowPosition);

    // 箭头前应该有参数相关的模式
    const validBeforePatterns = [
      /\w\s*$/, // 单参数
      /\)\s*$/, // 参数列表结束
      /\]\s*$/, // 数组解构结束
      /\}\s*$/, // 对象解构结束
      /:\s*\w+\s*$/, // 类型注解
      /async\s+\w\s*$/, // async 单参数
    ];

    // 检查箭头后的内容是否像函数体
    const afterArrow = this.sourceCode.slice(arrowPosition + 2, arrowPosition + 12);
    const validAfterPatterns = [
      /^\s*\{/, // 块语句开始
      /^\s*\w/, // 表达式开始
      /^\s*\(/, // 括号表达式
      /^\s*\[/, // 数组字面量
      /^\s*\{.*\}/, // 对象字面量
      /^\s*async/, // 异步表达式
    ];

    const hasValidBefore = validBeforePatterns.some((pattern) => pattern.test(beforeArrow));
    const hasValidAfter = validAfterPatterns.some((pattern) => pattern.test(afterArrow));

    return hasValidBefore && hasValidAfter;
  }

  /**
   * 查找无参数箭头函数的参数列表开始位置
   *
   * @param functionStart 函数起始位置
   * @returns 参数列表开始位置（即开括号位置）
   */
  private findEmptyParameterListStart(functionStart: number): number | null {
    // 在函数起始位置附近查找 '('
    const searchEnd = Math.min(this.sourceCode.length, functionStart + 30);
    const searchText = this.sourceCode.slice(functionStart, searchEnd);
    const parenIndex = searchText.indexOf('(');

    return parenIndex >= 0 ? functionStart + parenIndex : null;
  }

  /**
   * 记录箭头函数位置查找结果
   *
   * @param _node 箭头函数节点（未使用，保留用于日志）
   * @param position 找到的位置
   * @param method 使用的方法
   */
  private recordArrowFunctionPositionResult(_node: any, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'arrow-function-position',
            description: `箭头函数位置查找`,
            context: `方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'}箭头函数位置${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record arrow function position result:`, error);
      }
    }
  }

  /**
   * 记录箭头函数位置查找错误
   *
   * @param _node 箭头函数节点（未使用，保留用于日志）
   * @param error 错误信息
   */
  private recordArrowFunctionPositionError(_node: any, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'arrow-function-position-error',
            description: '箭头函数位置查找失败',
            context: error.message,
          },
          `箭头函数位置查找过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.error(`Failed to record arrow function position error:`, detailError);
      }
    }
  }

  // =============================================================================
  // 专用节点定位实例方法 - Task 8 增强实现
  // =============================================================================

  /**
   * 实例方法：查找三元运算符中的 '?' 位置
   * 支持各种三元运算符的精确定位
   *
   * @param node 条件表达式节点
   * @returns '?' 符号位置
   */
  public findTernaryOperatorPosition(node: any): number | null {
    if (!node || node.type !== 'ConditionalExpression') {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getTernarySearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 在范围内搜索 '?' 符号
      const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);

      // 使用正则表达式查找 '?' 符号，避免误匹配
      const questionRegex = /\s*\?\s*/g;
      const match = questionRegex.exec(searchText);

      if (match) {
        const questionIndex = match.index + match[0].indexOf('?');
        return searchRange.start + questionIndex;
      }

      return null;
    } catch (error) {
      // 错误处理：记录错误但不中断流程
      if (this.detailCollector) {
        try {
          this.detailCollector.addErrorStep(
            {
              line: 0,
              column: 0,
              increment: 0,
              ruleId: 'ternary-operator-position-error',
              description: '三元运算符位置查找失败',
              context: error instanceof Error ? error.message : String(error),
            },
            `三元运算符位置查找过程中发生错误: ${error instanceof Error ? error.message : String(error)}`
          );
        } catch (detailError) {
          console.warn(`Failed to record ternary operator position error:`, detailError);
        }
      }
      return null;
    }
  }

  /**
   * 确定三元运算符的搜索范围
   * 基于测试条件和真值表达式的位置
   *
   * @param node 条件表达式节点
   * @returns 搜索范围
   */
  private getTernarySearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 确定搜索起始位置：从测试条件结束后开始
    if (node.test && node.test.span && typeof node.test.span.end === 'number') {
      searchStart = node.test.span.end;
    } else if (node.span && typeof node.span.start === 'number') {
      searchStart = node.span.start;
    }

    // 确定搜索结束位置：到真值表达式开始前结束
    if (node.consequent && node.consequent.span && typeof node.consequent.span.start === 'number') {
      searchEnd = node.consequent.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 实例方法：查找方法定义中对应的关键字位置
   * 支持各种方法定义类型（get, set, async, static, 普通方法等）
   *
   * @param node 方法定义节点
   * @returns 关键字位置
   */
  public findMethodKeywordPosition(node: any): number | null {
    if (!node || node.type !== 'MethodDefinition') {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getMethodSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 根据方法类型查找相应的关键字
      const keywords = this.getMethodKeywords(node);

      for (const keyword of keywords) {
        const position = this.findKeywordInRange(keyword, searchRange);
        if (position !== null) {
          return position;
        }
      }

      return null;
    } catch (error) {
      // 错误处理：记录错误但不中断流程
      if (this.detailCollector) {
        try {
          this.detailCollector.addErrorStep(
            {
              line: 0,
              column: 0,
              increment: 0,
              ruleId: 'method-keyword-position-error',
              description: '方法关键字位置查找失败',
              context: error instanceof Error ? error.message : String(error),
            },
            `方法关键字位置查找过程中发生错误: ${error instanceof Error ? error.message : String(error)}`
          );
        } catch (detailError) {
          console.warn(`Failed to record method keyword position error:`, detailError);
        }
      }
      return null;
    }
  }

  /**
   * 确定方法定义的搜索范围
   *
   * @param node 方法定义节点
   * @returns 搜索范围
   */
  private getMethodSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 使用方法节点的 span 作为搜索范围
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      searchStart = node.span.start;
      // 限制搜索范围到方法名开始位置
      if (node.key && node.key.span && typeof node.key.span.start === 'number') {
        searchEnd = node.key.span.start + 50; // 给一些缓冲空间
      } else {
        searchEnd = node.span.start + 100; // 默认搜索前100个字符
      }
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 根据方法类型获取可能的关键字列表
   *
   * @param node 方法定义节点
   * @returns 关键字列表
   */
  private getMethodKeywords(node: any): string[] {
    const keywords: string[] = [];

    // 检查方法修饰符
    if (node.static) {
      keywords.push('static');
    }

    if (node.async) {
      keywords.push('async');
    }

    // 检查方法类型
    switch (node.kind) {
      case 'get':
        keywords.push('get');
        break;
      case 'set':
        keywords.push('set');
        break;
      case 'constructor':
        keywords.push('constructor');
        break;
      default:
        // 普通方法，如果有关键字则添加
        if (node.function && node.function.async) {
          keywords.push('async');
        }
        break;
    }

    // 如果没有找到特定关键字，添加方法名作为查找目标
    if (keywords.length === 0 && node.key) {
      const methodName = node.key.value || node.key.name;
      if (methodName) {
        keywords.push(methodName);
      }
    }

    return keywords;
  }

  /**
   * 实例方法：查找类型注解中的 ':' 位置
   * 支持 TypeScript 类型注解的精确定位
   *
   * @param node 类型注解节点
   * @returns ':' 符号位置
   */
  public findTypeColonPosition(node: any): number | null {
    if (!node || (!node.type.includes('TypeAnnotation') && node.type !== 'TSTypeAnnotation')) {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getTypeAnnotationSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 在范围内搜索 ':' 符号
      const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);

      // 使用正则表达式查找 ':' 符号，避免误匹配对象属性
      const colonRegex = /\s*:\s*(?![:])/g; // 避免匹配 :: 等情况
      const match = colonRegex.exec(searchText);

      if (match) {
        const colonIndex = match.index + match[0].indexOf(':');
        return searchRange.start + colonIndex;
      }

      return null;
    } catch (error) {
      // 错误处理：记录错误但不中断流程
      if (this.detailCollector) {
        try {
          this.detailCollector.addErrorStep(
            {
              line: 0,
              column: 0,
              increment: 0,
              ruleId: 'type-colon-position-error',
              description: '类型注解冒号位置查找失败',
              context: error instanceof Error ? error.message : String(error),
            },
            `类型注解冒号位置查找过程中发生错误: ${error instanceof Error ? error.message : String(error)}`
          );
        } catch (detailError) {
          console.warn(`Failed to record type colon position error:`, detailError);
        }
      }
      return null;
    }
  }

  /**
   * 确定类型注解的搜索范围
   *
   * @param node 类型注解节点
   * @returns 搜索范围
   */
  private getTypeAnnotationSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 使用类型注解节点的 span 作为搜索范围
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      // 向前搜索一些字符以找到参数名或变量名
      searchStart = Math.max(0, node.span.start - 30);
      searchEnd = node.span.start + 10; // 在类型注解开始附近查找
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 在指定范围内查找关键字
   *
   * @param keyword 要查找的关键字
   * @param range 搜索范围
   * @returns 关键字位置，如果未找到则返回 null
   */
  private findKeywordInRange(keyword: string, range: { start: number; end: number }): number | null {
    try {
      const searchText = this.sourceCode.slice(range.start, range.end);

      // 使用词边界正则表达式进行精确匹配
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'g');
      const match = keywordRegex.exec(searchText);

      if (match) {
        return range.start + match.index;
      }

      return null;
    } catch (error) {
      console.debug(`Keyword search failed for '${keyword}':`, error);
      return null;
    }
  }

  // =============================================================================
  // Task 10: 剩余节点类型定位实例方法 - 链式调用、类型定义、复杂嵌套
  // =============================================================================

  /**
   * 实例方法：查找成员访问操作符位置
   * 支持各种成员访问形式：obj.prop, obj?.prop
   *
   * @param node 成员表达式节点
   * @returns 操作符位置，如果未找到则返回 null
   */
  public findMemberAccessPosition(node: any): number | null {
    if (!node || (node.type !== 'MemberExpression' && node.type !== 'MemberExp')) {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getMemberExpressionSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 策略1：查找普通成员访问 '.'
      const dotPosition = this.findOperatorInRange('.', searchRange);
      if (dotPosition !== null) {
        this.recordMemberSearchResult(node, dotPosition, 'dot-operator');
        return dotPosition;
      }

      // 策略2：查找可选成员访问 '?.'
      const optionalPosition = this.findOperatorInRange('?.', searchRange);
      if (optionalPosition !== null) {
        this.recordMemberSearchResult(node, optionalPosition, 'optional-dot-operator');
        return optionalPosition;
      }

      // 策略3：基于对象和属性位置推断
      const inferredPosition = this.inferMemberAccessPosition(node);
      if (inferredPosition !== null) {
        this.recordMemberSearchResult(node, inferredPosition, 'position-inference');
        return inferredPosition;
      }

      // 所有策略都失败
      this.recordMemberSearchResult(node, null, 'all-failed');
      return null;
    } catch (error) {
      this.recordMemberSearchError(node, error as Error);
      return null;
    }
  }

  /**
   * 实例方法：查找计算成员访问位置
   * 支持 obj[key] 和 obj?.[key] 形式
   *
   * @param node 计算成员表达式节点
   * @returns 开括号位置，如果未找到则返回 null
   */
  public findComputedMemberAccessPosition(node: any): number | null {
    if (!node || node.type !== 'ComputedMemberExpression') {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getComputedMemberSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 策略1：查找开括号 '['
      const bracketPosition = this.findOperatorInRange('[', searchRange);
      if (bracketPosition !== null) {
        this.recordComputedMemberResult(node, bracketPosition, 'bracket-operator');
        return bracketPosition;
      }

      // 策略2：查找可选计算成员访问 '?.['
      const optionalBracketPosition = this.findOperatorInRange('?.[', searchRange);
      if (optionalBracketPosition !== null) {
        this.recordComputedMemberResult(node, optionalBracketPosition, 'optional-bracket-operator');
        return optionalBracketPosition;
      }

      // 策略3：基于对象位置推断
      if (node.object && node.object.span && typeof node.object.span.end === 'number') {
        this.recordComputedMemberResult(node, node.object.span.end, 'object-end-inference');
        return node.object.span.end;
      }

      // 所有策略都失败
      this.recordComputedMemberResult(node, null, 'all-failed');
      return null;
    } catch (error) {
      this.recordComputedMemberError(node, error as Error);
      return null;
    }
  }

  /**
   * 实例方法：查找可选链操作符位置
   * 支持 obj?.prop, obj?.method(), obj?.[key] 等形式
   *
   * @param node 可选链表达式节点
   * @returns 操作符位置，如果未找到则返回 null
   */
  public findOptionalChainingPosition(node: any): number | null {
    if (!node || node.type !== 'OptionalChainingExpression') {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getOptionalChainingSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 策略1：查找可选链操作符 '?.'
      const optionalPosition = this.findOperatorInRange('?.', searchRange);
      if (optionalPosition !== null) {
        this.recordOptionalChainingResult(node, optionalPosition, 'optional-chaining-operator');
        return optionalPosition;
      }

      // 策略2：查找空值合并操作符 '??'（某些情况下）
      const nullishPosition = this.findOperatorInRange('??', searchRange);
      if (nullishPosition !== null) {
        this.recordOptionalChainingResult(node, nullishPosition, 'nullish-coalescing-operator');
        return nullishPosition;
      }

      // 策略3：基于基础表达式推断
      if (node.base && node.base.span && typeof node.base.span.end === 'number') {
        this.recordOptionalChainingResult(node, node.base.span.end, 'base-end-inference');
        return node.base.span.end;
      }

      // 所有策略都失败
      this.recordOptionalChainingResult(node, null, 'all-failed');
      return null;
    } catch (error) {
      this.recordOptionalChainingError(node, error as Error);
      return null;
    }
  }

  /**
   * 实例方法：查找块语句开大括号位置
   * 支持函数体、控制流语句块等各种块语句
   *
   * @param node 块语句节点
   * @returns 开大括号位置，如果未找到则返回 null
   */
  public findBlockOpeningBracePosition(node: any): number | null {
    if (!node || node.type !== 'BlockStatement') {
      return null;
    }

    try {
      // 确定搜索范围
      const searchRange = this.getBlockStatementSearchRange(node);
      if (!searchRange) {
        return null;
      }

      // 策略1：查找开大括号 '{'
      const bracePosition = this.findOperatorInRange('{', searchRange);
      if (bracePosition !== null) {
        this.recordBlockStatementResult(node, bracePosition, 'opening-brace');
        return bracePosition;
      }

      // 策略2：基于父节点推断（如 if、while 等控制流语句）
      const parentBasedPosition = this.inferBlockPositionFromParent(node);
      if (parentBasedPosition !== null) {
        this.recordBlockStatementResult(node, parentBasedPosition, 'parent-inference');
        return parentBasedPosition;
      }

      // 策略3：使用第一个语句的位置
      if (node.stmts && node.stmts.length > 0) {
        const firstStmt = node.stmts[0];
        if (firstStmt && firstStmt.span && typeof firstStmt.span.start === 'number') {
          // 回退到第一个语句前的位置
          const inferredBrace = Math.max(0, firstStmt.span.start - 10);
          this.recordBlockStatementResult(node, inferredBrace, 'first-statement-inference');
          return inferredBrace;
        }
      }

      // 所有策略都失败
      this.recordBlockStatementResult(node, null, 'all-failed');
      return null;
    } catch (error) {
      this.recordBlockStatementError(node, error as Error);
      return null;
    }
  }

  // =============================================================================
  // 辅助方法：搜索范围确定和操作符查找
  // =============================================================================

  /**
   * 确定成员表达式的搜索范围
   *
   * @param node 成员表达式节点
   * @returns 搜索范围
   */
  private getMemberExpressionSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 基于对象部分确定起始位置
    if (node.object && node.object.span && typeof node.object.span.end === 'number') {
      searchStart = node.object.span.end;
    } else if (node.span && typeof node.span.start === 'number') {
      searchStart = node.span.start;
    }

    // 基于属性部分确定结束位置
    if (node.property && node.property.span && typeof node.property.span.start === 'number') {
      searchEnd = node.property.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 确定计算成员表达式的搜索范围
   *
   * @param node 计算成员表达式节点
   * @returns 搜索范围
   */
  private getComputedMemberSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 基于对象部分确定起始位置
    if (node.object && node.object.span && typeof node.object.span.end === 'number') {
      searchStart = node.object.span.end;
    } else if (node.span && typeof node.span.start === 'number') {
      searchStart = node.span.start;
    }

    // 基于属性部分确定结束位置
    if (node.property && node.property.span && typeof node.property.span.start === 'number') {
      searchEnd = node.property.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 确定可选链表达式的搜索范围
   *
   * @param node 可选链表达式节点
   * @returns 搜索范围
   */
  private getOptionalChainingSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 基于基础表达式确定起始位置
    if (node.base && node.base.span) {
      searchStart = node.base.span.start;
      searchEnd = node.base.span.end + 20; // 向后搜索20个字符
    } else if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      searchStart = node.span.start;
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 确定块语句的搜索范围
   *
   * @param node 块语句节点
   * @returns 搜索范围
   */
  private getBlockStatementSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 使用节点自身的 span
    if (node.span && typeof node.span.start === 'number' && typeof node.span.end === 'number') {
      searchStart = Math.max(0, node.span.start - 30); // 向前搜索30个字符
      searchEnd = Math.min(this.sourceCode.length, node.span.start + 30); // 向后搜索30个字符
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 在指定范围内查找操作符
   *
   * @param operator 要查找的操作符
   * @param range 搜索范围
   * @returns 操作符位置，如果未找到则返回 null
   */
  private findOperatorInRange(operator: string, range: { start: number; end: number }): number | null {
    try {
      const searchText = this.sourceCode.slice(range.start, range.end);

      // 使用正则表达式进行精确匹配，避免误匹配
      const escapedOperator = this.escapeRegExp(operator);
      const operatorRegex = new RegExp(escapedOperator, 'g');
      const match = operatorRegex.exec(searchText);

      if (match) {
        return range.start + match.index;
      }

      return null;
    } catch (error) {
      console.debug(`Operator search failed for '${operator}':`, error);
      return null;
    }
  }

  /**
   * 基于对象和属性位置推断成员访问位置
   *
   * @param node 成员表达式节点
   * @returns 推断的位置，如果无法推断则返回 null
   */
  private inferMemberAccessPosition(node: any): number | null {
    // 如果有对象和属性的span信息，推断访问符位置
    if (
      node.object &&
      node.object.span &&
      node.property &&
      node.property.span &&
      typeof node.object.span.end === 'number' &&
      typeof node.property.span.start === 'number'
    ) {
      // 访问符应该在对象结束和属性开始之间
      const objectEnd = node.object.span.end;
      const propertyStart = node.property.span.start;

      if (objectEnd < propertyStart) {
        // 返回中间位置
        return Math.floor((objectEnd + propertyStart) / 2);
      }
    }

    return null;
  }

  /**
   * 基于父节点推断块语句位置
   *
   * @param _node 块语句节点
   * @returns 推断的位置，如果无法推断则返回 null
   */
  private inferBlockPositionFromParent(_node: any): number | null {
    const parent = this.getParent();
    if (!parent) {
      return null;
    }

    // 基于父节点类型推断块的位置
    switch (parent.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'ForStatement':
      case 'DoWhileStatement':
        // 对于控制流语句，块通常在条件之后
        if (parent.span && typeof parent.span.start === 'number') {
          return parent.span.start;
        }
        break;

      case 'FunctionDeclaration':
      case 'FunctionExpression':
      case 'MethodDefinition':
        // 对于函数，块通常在参数列表之后
        const params = (parent as any).params;
        if (params && params.length > 0) {
          const lastParam = params[params.length - 1];
          if (lastParam.span && typeof lastParam.span.end === 'number') {
            return lastParam.span.end;
          }
        }
        break;
    }

    return null;
  }

  // =============================================================================
  // 结果记录方法：用于调试和日志记录
  // =============================================================================

  /**
   * 记录成员访问搜索结果
   */
  private recordMemberSearchResult(node: any, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'member-access-search',
            description: `成员访问搜索`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'}成员访问操作符${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record member search result:`, error);
      }
    }
  }

  /**
   * 记录成员访问搜索错误
   */
  private recordMemberSearchError(node: any, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'member-access-search-error',
            description: '成员访问搜索失败',
            context: error.message,
          },
          `成员访问搜索过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.warn(`Failed to record member search error:`, detailError);
      }
    }
  }

  /**
   * 记录计算成员访问搜索结果
   */
  private recordComputedMemberResult(node: any, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'computed-member-search',
            description: `计算成员访问搜索`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'}计算成员访问操作符${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record computed member result:`, error);
      }
    }
  }

  /**
   * 记录计算成员访问搜索错误
   */
  private recordComputedMemberError(node: any, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'computed-member-search-error',
            description: '计算成员访问搜索失败',
            context: error.message,
          },
          `计算成员访问搜索过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.warn(`Failed to record computed member error:`, detailError);
      }
    }
  }

  /**
   * 记录可选链搜索结果
   */
  private recordOptionalChainingResult(node: any, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'optional-chaining-search',
            description: `可选链搜索`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'}可选链操作符${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record optional chaining result:`, error);
      }
    }
  }

  /**
   * 记录可选链搜索错误
   */
  private recordOptionalChainingError(node: any, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'optional-chaining-search-error',
            description: '可选链搜索失败',
            context: error.message,
          },
          `可选链搜索过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.warn(`Failed to record optional chaining error:`, detailError);
      }
    }
  }

  /**
   * 记录块语句搜索结果
   */
  private recordBlockStatementResult(node: any, position: number | null, method: string): void {
    if (this.detailCollector) {
      try {
        const resultPosition =
          position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };

        this.detailCollector.addStepWithDiagnostic(
          {
            line: resultPosition.line,
            column: resultPosition.column,
            increment: 0,
            ruleId: 'block-statement-search',
            description: `块语句搜索`,
            context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? '成功' : '失败'}`,
          },
          position !== null ? ('DEBUG' as any) : ('WARNING' as any),
          `使用 ${method} 方法${position !== null ? '成功找到' : '未找到'}块语句开大括号${
            position !== null ? ` (位置: ${position})` : ''
          }`
        );
      } catch (error) {
        console.warn(`Failed to record block statement result:`, error);
      }
    }
  }

  /**
   * 记录块语句搜索错误
   */
  private recordBlockStatementError(node: any, error: Error): void {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep(
          {
            line: 0,
            column: 0,
            increment: 0,
            ruleId: 'block-statement-search-error',
            description: '块语句搜索失败',
            context: error.message,
          },
          `块语句搜索过程中发生错误: ${error.message}`
        );
      } catch (detailError) {
        console.warn(`Failed to record block statement error:`, detailError);
      }
    }
  }

  /**
   * 检测逻辑运算符
   * @param node 节点
   * @returns 是否为逻辑运算符
   */
  private isLogicalOperator(node: any): boolean {
    return node.operator === '&&' || node.operator === '||' || node.operator === '??';
  }

  /**
   * 检测是否为默认值赋值（需要排除）
   * @param node 节点
   * @returns 是否为默认值赋值
   */
  private isDefaultValueAssignment(node: any): boolean {
    // 检查 ?? 空值合并操作符 (总是应该被排除)
    if (node.operator === '??') {
      return true;
    }

    // 检查 || 用于默认值赋值的情况
    if (node.operator === '||') {
      return this.isInDefaultValueAssignmentContext(node);
    }

    // 检查 && 在某些默认值赋值模式中的使用
    if (node.operator === '&&') {
      return this.isPartOfDefaultValuePattern(node);
    }

    return false;
  }

  /**
   * 检测节点是否在默认值赋值上下文中
   * 简化版本：检查是否在赋值上下文中
   * @param node 节点
   * @returns 是否在赋值上下文中
   */
  private isInDefaultValueAssignmentContext(node: any): boolean {
    // 简化实现：检查父节点类型
    const parent = this.getParent();
    if (!parent) {
      return false;
    }

    // 检查是否在变量声明或赋值表达式中
    return parent.type === 'VariableDeclarator' || parent.type === 'AssignmentExpression';
  }

  /**
   * 检测 && 运算符是否是默认值赋值模式的一部分
   * @param node 节点
   * @returns 是否是默认值模式的一部分
   */
  private isPartOfDefaultValuePattern(node: any): boolean {
    // 简化实现：检查是否在赋值上下文中
    return this.isInDefaultValueAssignmentContext(node);
  }

  /**
   * 检测递归调用
   * @param node 调用节点
   * @returns 是否为递归调用
   */
  private isRecursiveCall(node: any): boolean {
    // 获取调用的函数名
    const callee = this.getCalleeIdentifier(node);
    if (!callee || !this.currentFunctionName) {
      return false;
    }

    // 比较调用名与当前函数名
    return callee === this.currentFunctionName;
  }

  /**
   * 获取调用表达式的函数标识符
   * @param callNode 调用节点
   * @returns 函数标识符
   */
  private getCalleeIdentifier(callNode: any): string | null {
    const callee = callNode.callee;

    if (!callee) {
      return null;
    }

    // 直接函数调用: functionName()
    if (callee.type === 'Identifier') {
      return callee.value || callee.name;
    }

    // 成员表达式调用: obj.method() - 检查是否是this.method()
    if (callee.type === 'MemberExpression') {
      const object = callee.object;
      const property = callee.property;

      // this.functionName() 的情况
      if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
        return property.value || property.name;
      }
    }

    return null;
  }

  /**
   * 检查并应用混用惩罚（确保每个表达式树只应用一次）
   * @param node 当前逻辑运算符节点
   */
  private checkAndApplyMixingPenaltyOnce(node: any): void {
    // 查找逻辑表达式树的根节点
    const rootNode = this.findLogicalExpressionRoot(node);

    // 如果这个表达式树已经处理过混用惩罚，跳过
    if (this.processedMixingNodes.has(rootNode)) {
      return;
    }

    // 检查整个表达式树是否存在混用
    if (this.detectLogicalOperatorMixing(rootNode)) {
      this.addComplexity(1, rootNode, 'logical-operator-mixing', '逻辑运算符混用惩罚', '混用不同类型的逻辑运算符');

      // 标记这个表达式树已经处理过
      this.processedMixingNodes.add(rootNode);
    }
  }

  /**
   * 检测逻辑运算符混用
   * 完整版本实现，支持括号豁免检测
   * @param node 逻辑运算符节点
   * @returns 是否检测到混用
   */
  private detectLogicalOperatorMixing(node: any): boolean {
    // 检查配置是否启用混用检测
    if (!this.options.enableMixedLogicOperatorPenalty) {
      return false;
    }

    const currentOperator = (node as any).operator;
    if (!currentOperator || !['&&', '||'].includes(currentOperator)) {
      return false;
    }

    // 早期退出：检查是否为默认值赋值（需要豁免）
    if (this.isDefaultValueAssignment(node)) {
      return false;
    }

    // 检查括号豁免 - 优先检查，避免昂贵的运算符收集
    if (this.hasParenthesizedOperands(node)) {
      return false;
    }

    // 检查子节点是否包含不同的逻辑运算符
    const hasConflictingOperators = this.hasConflictingLogicalOperators(node, currentOperator);

    // 检查父级是否包含不同的逻辑运算符
    const parent = this.getParent();
    const parentHasConflict =
      parent &&
      this.isLogicalExpressionType(parent) &&
      (parent as any).operator !== currentOperator &&
      ['&&', '||'].includes((parent as any).operator);

    return hasConflictingOperators || Boolean(parentHasConflict);
  }

  /**
   * 检查逻辑表达式的操作数是否被括号包裹
   * 支持括号豁免机制
   * @param node 逻辑运算符节点
   * @returns 是否被括号包裹
   */
  private hasParenthesizedOperands(node: any): boolean {
    // 直接检查当前节点的左右操作数是否被括号包裹
    if (this.isDirectlyParenthesized(node.left) || this.isDirectlyParenthesized(node.right)) {
      return true;
    }

    // 检查是否存在逻辑分组的括号（消除歧义的括号）
    if (this.hasLogicalGroupingParentheses(node)) {
      return true;
    }

    return false;
  }

  /**
   * 检查节点是否直接被括号包裹
   * @param node 节点
   * @returns 是否被括号包裹
   */
  private isDirectlyParenthesized(node: any): boolean {
    return node && node.type === 'ParenthesizedExpression';
  }

  /**
   * 检查是否存在逻辑分组的括号
   * 例如: (a && b) || c 或 a && (b || c)
   * @param node 节点
   * @returns 是否存在逻辑分组括号
   */
  private hasLogicalGroupingParentheses(node: any): boolean {
    if (!node) return false;

    // 检查左子表达式
    if (node.left && this.containsLogicalGrouping(node.left)) {
      return true;
    }

    // 检查右子表达式
    if (node.right && this.containsLogicalGrouping(node.right)) {
      return true;
    }

    return false;
  }

  /**
   * 递归检查表达式是否包含逻辑分组括号
   * @param node 节点
   * @returns 是否包含逻辑分组
   */
  private containsLogicalGrouping(node: any): boolean {
    if (!node || typeof node !== 'object') return false;

    // 如果是括号表达式且包含逻辑运算符，则认为是分组括号
    if (node.type === 'ParenthesizedExpression') {
      return this.containsLogicalOperators(node.expression);
    }

    // 递归检查子节点
    if (node.left && this.containsLogicalGrouping(node.left)) return true;
    if (node.right && this.containsLogicalGrouping(node.right)) return true;

    return false;
  }

  /**
   * 检查表达式是否包含逻辑运算符
   * @param node 节点
   * @returns 是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') return false;

    // 检查当前节点是否是逻辑运算符
    if (
      (node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
      ['&&', '||'].includes(node.operator)
    ) {
      return true;
    }

    // 递归检查子节点
    const checkNode = (childNode: any): boolean => {
      if (childNode && typeof childNode === 'object') {
        return this.containsLogicalOperators(childNode);
      }
      return false;
    };

    // 检查所有子属性
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;

      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(checkNode)) return true;
      } else if (checkNode(value)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查节点是否包含与当前运算符冲突的逻辑运算符
   * @param node 节点
   * @param currentOperator 当前运算符
   * @returns 是否有冲突
   */
  private hasConflictingLogicalOperators(node: any, currentOperator: string): boolean {
    if (!node) return false;

    // 检查左右子节点
    const checkChild = (child: any): boolean => {
      if (!child) return false;

      // 直接检查逻辑表达式
      if (this.isLogicalExpressionType(child)) {
        return child.operator !== currentOperator && ['&&', '||'].includes(child.operator);
      }

      // 处理括号表达式：深入检查括号内的表达式
      if (child.type === 'ParenthesisExpression' && child.expression) {
        return this.checkChildForLogicalOperator(child.expression, currentOperator);
      }

      // 递归检查子节点的子节点（最多一层）
      return this.hasConflictingLogicalOperators(child, currentOperator);
    };

    return checkChild(node.left) || checkChild(node.right);
  }

  /**
   * 检查子节点是否包含与当前操作符不同的逻辑操作符
   */
  private checkChildForLogicalOperator(child: any, currentOperator: string): boolean {
    if (!child) return false;

    if (this.isLogicalExpressionType(child)) {
      return child.operator !== currentOperator && ['&&', '||'].includes(child.operator);
    }

    // 递归检查更深层的节点
    if (child.left && this.checkChildForLogicalOperator(child.left, currentOperator)) {
      return true;
    }

    if (child.right && this.checkChildForLogicalOperator(child.right, currentOperator)) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否为逻辑表达式类型
   * @param node 节点
   * @returns 是否为逻辑表达式
   */
  private isLogicalExpressionType(node: any): boolean {
    return node && (node.type === 'LogicalExpression' || node.type === 'BinaryExpression');
  }

  /**
   * 查找逻辑表达式树的根节点
   * 向上查找，直到找到不是逻辑运算符的父节点
   * @param node 当前节点
   * @returns 逻辑表达式树的根节点
   */
  private findLogicalExpressionRoot(node: any): any {
    let current = node;
    let parent = this.getParent();

    // 向上查找，直到父节点不是逻辑表达式
    while (parent && this.isLogicalExpressionType(parent) && ['&&', '||'].includes((parent as any).operator)) {
      current = parent;
      // 这里需要模拟向上移动，但由于我们没有轻松的方式获取更高层的父节点
      // 我们使用一个简化的策略：如果当前节点的父节点是逻辑运算符，
      // 我们假设父节点就是根（这个假设在大多数情况下是正确的）
      break;
    }

    return current;
  }
}
