import type { Node } from '@swc/core';
import type { PositionStrategy, PositionStrategyEntry } from '../types';

/**
 * 节点位置策略映射表
 * 为不同类型的 AST 节点提供专门的位置定位策略
 * 支持运行时扩展和动态配置
 */
export const NODE_POSITION_STRATEGIES: Map<string, PositionStrategyEntry> = new Map([
  // 控制流语句策略
  [
    'IfStatement',
    {
      nodeType: 'IfStatement',
      strategy: createIfStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('if'),
    },
  ],
  [
    'WhileStatement',
    {
      nodeType: 'WhileStatement',
      strategy: createWhileStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('while'),
    },
  ],
  [
    'ForStatement',
    {
      nodeType: 'ForStatement',
      strategy: createForStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('for'),
    },
  ],
  // 箭头函数策略
  [
    'ArrowFunctionExpression',
    {
      nodeType: 'ArrowFunctionExpression',
      strategy: createArrowFunctionStrategy(),
      priority: 1,
      fallbackStrategy: createArrowFunctionFallbackStrategy(),
    },
  ],
  // JSX 元素策略
  [
    'JSXElement',
    {
      nodeType: 'JSXElement',
      strategy: createJSXElementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('<'),
    },
  ],
  [
    'JSXFragment',
    {
      nodeType: 'JSXFragment',
      strategy: createJSXElementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('<'),
    },
  ],
  [
    'JSXExpressionContainer',
    {
      nodeType: 'JSXExpressionContainer',
      strategy: createJSXExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('{'),
    },
  ],
  // 其他控制流语句策略
  [
    'DoWhileStatement',
    {
      nodeType: 'DoWhileStatement',
      strategy: createDoWhileStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('do'),
    },
  ],
  [
    'SwitchStatement',
    {
      nodeType: 'SwitchStatement',
      strategy: createSwitchStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('switch'),
    },
  ],
  [
    'TryStatement',
    {
      nodeType: 'TryStatement',
      strategy: createTryStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('try'),
    },
  ],
  [
    'CatchClause',
    {
      nodeType: 'CatchClause',
      strategy: createCatchClauseStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('catch'),
    },
  ],
  // 条件表达式策略（三元运算符）
  [
    'ConditionalExpression',
    {
      nodeType: 'ConditionalExpression',
      strategy: createConditionalExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('?'),
    },
  ],
  // 函数表达式策略
  [
    'FunctionExpression',
    {
      nodeType: 'FunctionExpression',
      strategy: createFunctionExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('function'),
    },
  ],
  [
    'FunctionDeclaration',
    {
      nodeType: 'FunctionDeclaration',
      strategy: createFunctionDeclarationStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('function'),
    },
  ],
  [
    'MethodDefinition',
    {
      nodeType: 'MethodDefinition',
      strategy: createMethodDefinitionStrategy(),
      priority: 2, // 更高优先级，因为可能有多种关键字
      fallbackStrategy: createControlFlowFallbackStrategy('('),
    },
  ],
  // 类型定义策略
  [
    'TypeAnnotation',
    {
      nodeType: 'TypeAnnotation',
      strategy: createTypeAnnotationStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy(':'),
    },
  ],
  [
    'TSTypeAnnotation',
    {
      nodeType: 'TSTypeAnnotation',
      strategy: createTypeAnnotationStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy(':'),
    },
  ],
  // 链式调用策略（MemberExpression）
  [
    'MemberExpression',
    {
      nodeType: 'MemberExpression',
      strategy: createMemberExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('.'),
    },
  ],
  // 成员表达式别名策略
  [
    'MemberExp',
    {
      nodeType: 'MemberExp',
      strategy: createMemberExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('.'),
    },
  ],
  // 计算成员表达式（可选链）策略
  [
    'ComputedMemberExpression',
    {
      nodeType: 'ComputedMemberExpression',
      strategy: createComputedMemberExpressionStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('['),
    },
  ],
  // 可选链式调用策略
  [
    'OptionalChainingExpression',
    {
      nodeType: 'OptionalChainingExpression',
      strategy: createOptionalChainingStrategy(),
      priority: 2, // 更高优先级，因为需要处理 ?. ?? 等操作符
      fallbackStrategy: createControlFlowFallbackStrategy('?.'),
    },
  ],
  // 块语句策略
  [
    'BlockStatement',
    {
      nodeType: 'BlockStatement',
      strategy: createBlockStatementStrategy(),
      priority: 1,
      fallbackStrategy: createControlFlowFallbackStrategy('{'),
    },
  ],
]);

/**
 * 注册新的节点位置策略
 * 支持运行时扩展策略映射表
 * @param entry 策略映射表条目
 */
export function registerPositionStrategy(entry: PositionStrategyEntry): void {
  NODE_POSITION_STRATEGIES.set(entry.nodeType, entry);
}

/**
 * 获取指定节点类型的位置策略
 * @param nodeType 节点类型
 * @returns 策略映射条目，如果不存在则返回 undefined
 */
export function getPositionStrategy(nodeType: string): PositionStrategyEntry | undefined {
  return NODE_POSITION_STRATEGIES.get(nodeType);
}

// =============================================================================
// 策略函数工厂方法 - 创建具体的位置定位策略
// =============================================================================

/**
 * 创建 IfStatement 节点的位置策略
 * 优先查找 'if' 关键字位置
 */
export function createIfStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'if' 关键字
    const ifPosition = findKeywordInNode(node, 'if');
    if (ifPosition !== null) {
      return ifPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 WhileStatement 节点的位置策略
 * 优先查找 'while' 关键字位置
 */
export function createWhileStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'while' 关键字
    const whilePosition = findKeywordInNode(node, 'while');
    if (whilePosition !== null) {
      return whilePosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 ForStatement 节点的位置策略
 * 优先查找 'for' 关键字位置
 */
export function createForStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'for' 关键字
    const forPosition = findKeywordInNode(node, 'for');
    if (forPosition !== null) {
      return forPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 ArrowFunctionExpression 节点的位置策略
 * 专门处理箭头函数的精确定位，支持多种形式的箭头函数
 */
export function createArrowFunctionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const arrowNode = node as any;

    // 策略1：直接查找箭头符号 '=>' 位置
    const arrowPosition = findArrowOperatorPosition(arrowNode);
    if (arrowPosition !== null) {
      return arrowPosition;
    }

    // 策略2：基于参数列表定位
    const parameterPosition = findArrowFunctionParameterPosition(arrowNode);
    if (parameterPosition !== null) {
      return parameterPosition;
    }

    // 策略3：回退到节点自身的 span
    if (arrowNode.span && typeof arrowNode.span.start === 'number') {
      return arrowNode.span.start;
    }

    return null;
  };
}

/**
 * 创建箭头函数的回退策略
 * 当主策略失败时使用的回退逻辑
 */
export function createArrowFunctionFallbackStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const arrowNode = node as any;

    // 回退策略1：查找参数的起始位置
    if (arrowNode.params && arrowNode.params.length > 0) {
      const firstParam = arrowNode.params[0];
      if (firstParam && firstParam.span && typeof firstParam.span.start === 'number') {
        return firstParam.span.start;
      }
    }

    // 回退策略2：使用节点span
    if (arrowNode.span && typeof arrowNode.span.start === 'number') {
      return arrowNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 JSXElement 节点的位置策略
 * 优先查找 JSX 开放标签位置
 */
export function createJSXElementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 JSX 开放标签查找
    const jsxPosition = findJsxOpeningTagPositionStatic(node);
    if (jsxPosition !== null) {
      return jsxPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 JSXExpressionContainer 节点的位置策略
 * 优先查找 JSX 表达式内容位置
 */
export function createJSXExpressionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 JSX 表达式内容查找
    const jsxExprPosition = findJSXExpressionContentPositionStatic(node);
    if (jsxExprPosition !== null) {
      return jsxExprPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 DoWhileStatement 节点的位置策略
 * 优先查找 'do' 关键字位置
 */
export function createDoWhileStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'do' 关键字
    const doPosition = findKeywordInNode(node, 'do');
    if (doPosition !== null) {
      return doPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 SwitchStatement 节点的位置策略
 * 优先查找 'switch' 关键字位置
 */
export function createSwitchStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'switch' 关键字
    const switchPosition = findKeywordInNode(node, 'switch');
    if (switchPosition !== null) {
      return switchPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 TryStatement 节点的位置策略
 * 优先查找 'try' 关键字位置
 */
export function createTryStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'try' 关键字
    const tryPosition = findKeywordInNode(node, 'try');
    if (tryPosition !== null) {
      return tryPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 CatchClause 节点的位置策略
 * 优先查找 'catch' 关键字位置
 */
export function createCatchClauseStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'catch' 关键字
    const catchPosition = findKeywordInNode(node, 'catch');
    if (catchPosition !== null) {
      return catchPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 ConditionalExpression 节点的位置策略（三元运算符）
 * 优先查找 '?' 符号位置
 */
export function createConditionalExpressionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const conditionalNode = node as any;

    // 策略1：直接查找三元运算符的 '?' 位置
    const questionPosition = findTernaryOperatorPosition(conditionalNode);
    if (questionPosition !== null) {
      return questionPosition;
    }

    // 策略2：基于测试条件定位
    if (conditionalNode.test && conditionalNode.test.span) {
      return conditionalNode.test.span.start;
    }

    // 策略3：回退到节点自身的 span
    if (conditionalNode.span && typeof conditionalNode.span.start === 'number') {
      return conditionalNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 FunctionExpression 节点的位置策略
 * 优先查找 'function' 关键字位置
 */
export function createFunctionExpressionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'function' 关键字
    const functionPosition = findKeywordInNode(node, 'function');
    if (functionPosition !== null) {
      return functionPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 FunctionDeclaration 节点的位置策略
 * 优先查找 'function' 关键字位置
 */
export function createFunctionDeclarationStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    // 尝试通过 Token 查找 'function' 关键字
    const functionPosition = findKeywordInNode(node, 'function');
    if (functionPosition !== null) {
      return functionPosition;
    }

    // 回退到节点自身的 span
    const nodeWithSpan = node as any;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === 'number') {
      return nodeWithSpan.span.start;
    }

    return null;
  };
}

/**
 * 创建 MethodDefinition 节点的位置策略
 * 支持多种方法定义形式（get, set, async, 普通方法等）
 */
export function createMethodDefinitionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const methodNode = node as any;

    // 策略1：根据方法类型查找相应关键字
    const keywordPosition = findMethodKeywordPosition(methodNode);
    if (keywordPosition !== null) {
      return keywordPosition;
    }

    // 策略2：基于方法名定位
    if (methodNode.key && methodNode.key.span) {
      return methodNode.key.span.start;
    }

    // 策略3：回退到节点自身的 span
    if (methodNode.span && typeof methodNode.span.start === 'number') {
      return methodNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 TypeAnnotation 节点的位置策略
 * 优先查找 ':' 符号位置
 */
export function createTypeAnnotationStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const typeNode = node as any;

    // 策略1：查找类型注解的 ':' 符号
    const colonPosition = findTypeColonPosition(typeNode);
    if (colonPosition !== null) {
      return colonPosition;
    }

    // 策略2：基于类型注解内容定位
    if (typeNode.typeAnnotation && typeNode.typeAnnotation.span) {
      return typeNode.typeAnnotation.span.start;
    }

    // 策略3：回退到节点自身的 span
    if (typeNode.span && typeof typeNode.span.start === 'number') {
      return typeNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 MemberExpression 节点的位置策略（链式调用）
 * 支持各种成员访问模式：obj.prop, obj?.prop, obj[key], obj?.[key]
 */
export function createMemberExpressionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const memberNode = node as any;

    // 策略1：直接查找成员访问操作符位置（'.' 或 '?.'）
    const memberPosition = findMemberAccessOperatorPosition(memberNode);
    if (memberPosition !== null) {
      return memberPosition;
    }

    // 策略2：基于对象部分定位
    if (memberNode.object && memberNode.object.span) {
      return memberNode.object.span.start;
    }

    // 策略3：回退到整个表达式的开始
    if (memberNode.span && typeof memberNode.span.start === 'number') {
      return memberNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 ComputedMemberExpression 节点的位置策略（计算成员访问）
 * 支持 obj[key] 和 obj?.[key] 形式
 */
export function createComputedMemberExpressionStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const computedNode = node as any;

    // 策略1：查找开括号 '[' 位置
    const bracketPosition = findComputedMemberBracketPosition(computedNode);
    if (bracketPosition !== null) {
      return bracketPosition;
    }

    // 策略2：基于对象部分定位
    if (computedNode.object && computedNode.object.span) {
      return computedNode.object.span.start;
    }

    // 策略3：回退到节点自身的 span
    if (computedNode.span && typeof computedNode.span.start === 'number') {
      return computedNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 OptionalChainingExpression 节点的位置策略（可选链）
 * 支持 obj?.prop, obj?.method(), obj?.[key] 等形式
 */
export function createOptionalChainingStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const optionalNode = node as any;

    // 策略1：查找可选链操作符 '?.' 位置
    const optionalPosition = findOptionalChainingOperatorPosition(optionalNode);
    if (optionalPosition !== null) {
      return optionalPosition;
    }

    // 策略2：基于基础表达式定位
    if (optionalNode.base && optionalNode.base.span) {
      return optionalNode.base.span.start;
    }

    // 策略3：基于对象部分定位（如果有）
    if (optionalNode.object && optionalNode.object.span) {
      return optionalNode.object.span.start;
    }

    // 策略4：回退到节点自身的 span
    if (optionalNode.span && typeof optionalNode.span.start === 'number') {
      return optionalNode.span.start;
    }

    return null;
  };
}

/**
 * 创建 BlockStatement 节点的位置策略
 * 支持各种块语句的精确定位
 */
export function createBlockStatementStrategy(): PositionStrategy {
  return (node: Node): number | null => {
    const blockNode = node as any;

    // 策略1：查找开大括号 '{' 位置
    const bracePosition = findBlockOpeningBracePosition(blockNode);
    if (bracePosition !== null) {
      return bracePosition;
    }

    // 策略2：使用第一个语句的位置
    if (blockNode.stmts && blockNode.stmts.length > 0) {
      const firstStmt = blockNode.stmts[0];
      if (firstStmt && firstStmt.span && typeof firstStmt.span.start === 'number') {
        return firstStmt.span.start;
      }
    }

    // 策略3：回退到节点自身的 span
    if (blockNode.span && typeof blockNode.span.start === 'number') {
      return blockNode.span.start;
    }

    return null;
  };
}

/**
 * 创建控制流语句的通用回退策略
 * 当主策略失败时使用的回退逻辑，现在支持实例级源代码访问
 * @param keyword 控制流关键字
 */
export function createControlFlowFallbackStrategy(keyword: string): PositionStrategy {
  return (node: Node): number | null => {
    // 静态方法无法访问源代码，返回 null
    // 实际的回退逻辑将由实例方法 applyPositionStrategy 处理
    return null;
  };
}

// =============================================================================
// Token 查找系统 - 基于 SWC Token 流的精确关键字定位
// =============================================================================

/**
 * 在节点中查找指定关键字的位置（静态版本）
 * 这是一个简化版本，实际实现会使用 SWC Token API
 * @param _node AST 节点（未使用，保留用于未来扩展）
 * @param _keyword 要查找的关键字（未使用，保留用于未来扩展）
 * @returns 关键字位置，如果未找到则返回 null
 */
export function findKeywordInNode(_node: Node, _keyword: string): number | null {
  // 简化实现：直接返回 null，由实例方法中的复杂逻辑处理
  // 在实际实现中，这里会使用 SWC Token API 进行精确查找
  return null;
}

// =============================================================================
// 箭头函数定位专用静态方法 - Task 6 实现
// =============================================================================

/**
 * 查找箭头函数中的箭头操作符 '=>' 位置（静态版本）
 * 由于无法访问源代码，返回 null，由实例方法处理
 * @param _node 箭头函数节点
 * @returns null（静态方法限制）
 */
export function findArrowOperatorPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找箭头函数参数列表的起始位置（静态版本）
 * @param node 箭头函数节点
 * @returns 参数列表起始位置，如果无法确定则返回 null
 */
export function findArrowFunctionParameterPosition(node: any): number | null {
  if (!node || node.type !== 'ArrowFunctionExpression') {
    return null;
  }

  // 情况1：有参数列表
  if (node.params && node.params.length > 0) {
    const firstParam = node.params[0];
    if (firstParam && firstParam.span && typeof firstParam.span.start === 'number') {
      return firstParam.span.start;
    }
  }

  // 情况2：无参数，使用节点 span
  if (node.span && typeof node.span.start === 'number') {
    return node.span.start;
  }

  return null;
}

/**
 * 查找 JSX 开放标签位置（静态版本）
 * 用于策略函数中的静态调用
 * @param _node JSX 节点
 * @returns JSX 开放标签位置，如果未找到则返回 null
 */
export function findJsxOpeningTagPositionStatic(_node: Node): number | null {
  // 简化实现：直接返回 null，由实例方法处理
  // 在实际实现中，这里会调用实例方法或使用更复杂的逻辑
  return null;
}

/**
 * 查找 JSX 表达式内容位置（静态版本）
 * 用于策略函数中的静态调用
 * @param _node JSX 表达式节点
 * @returns JSX 表达式内容位置，如果未找到则返回 null
 */
export function findJSXExpressionContentPositionStatic(_node: Node): number | null {
  // 简化实现：直接返回 null，由实例方法处理
  // 在实际实现中，这里会调用实例方法或使用更复杂的逻辑
  return null;
}

/**
 * 查找三元运算符中的 '?' 位置（静态版本）
 * @param _node 条件表达式节点
 * @returns '?' 位置，如果未找到则返回 null
 */
export function findTernaryOperatorPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找方法定义中对应的关键字位置（静态版本）
 * @param _node 方法定义节点
 * @returns 关键字位置，如果未找到则返回 null
 */
export function findMethodKeywordPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找类型注解中的 ':' 位置（静态版本）
 * @param _node 类型注解节点
 * @returns ':' 位置，如果未找到则返回 null
 */
export function findTypeColonPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找成员访问操作符位置（静态版本）
 * @param _node 成员表达式节点
 * @returns 操作符位置，如果未找到则返回 null
 */
export function findMemberAccessOperatorPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找计算成员访问的开括号位置（静态版本）
 * @param _node 计算成员表达式节点
 * @returns 开括号位置，如果未找到则返回 null
 */
export function findComputedMemberBracketPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找可选链操作符位置（静态版本）
 * @param _node 可选链表达式节点
 * @returns 操作符位置，如果未找到则返回 null
 */
export function findOptionalChainingOperatorPosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}

/**
 * 查找块语句开大括号位置（静态版本）
 * @param _node 块语句节点
 * @returns 开大括号位置，如果未找到则返回 null
 */
export function findBlockOpeningBracePosition(_node: any): number | null {
  // 静态方法无法访问源代码，由实例方法处理
  return null;
}
